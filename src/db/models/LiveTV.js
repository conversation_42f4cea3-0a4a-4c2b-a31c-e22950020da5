// NetStream_GraphQL/src/db/models/LiveTV.js
const mongoose = require('mongoose');
const Schema = mongoose.Schema;

const StreamingUrlSchema = new Schema({
  url: { type: String, required: true },
  provider: { type: String, default: 'unknown' },
  language: { type: String, enum: ['VF', 'VOSTFR', 'unknown'], default: 'unknown' },
  lastChecked: { type: Date, default: Date.now },
  isActive: { type: Boolean, default: true },
  sourceStreamUrl: { type: String, default: null },
  size: { type: String, default: null },   // Added
  type: { type: String, default: null },   // Added (HLS or MP4)
  method: { type: String, default: null } // Added
});

const TmdbSchema = new Schema({
  id: Number,
  title: String,
  overview: String,
  release_date: String,
  poster_path: String,
  vote_average: Number
});

const LiveTvSchema = new Schema({
  title: { type: String, required: true },
  detailUrl: { type: String, required: true },
  detailUrlPath: { type: String, required: false }, // Keep
  cleanedTitle: String,
  image: String,
  streamingUrls: [StreamingUrlSchema],
  tmdb: TmdbSchema,
  createdAt: { type: Date, default: Date.now },
  updatedAt: { type: Date, default: Date.now } // Added for consistency
}, { timestamps: true });

// Add pre-save hook to ensure updatedAt is set
LiveTvSchema.pre('save', function(next) {
    this.updatedAt = new Date();
    next();
});

module.exports = mongoose.model('LiveTV', LiveTvSchema, 'livetv');