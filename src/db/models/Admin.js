// src/db/models/Admin.js
const mongoose = require('mongoose');
const Schema = mongoose.Schema;
const crypto = require('crypto');

const AdminSchema = new Schema({
    token: { 
        type: String, 
        required: true,
        unique: true
    },
    createdAt: { 
        type: Date, 
        default: Date.now,
        expires: '30d' // Token expires after 30 days
    }
}, {
    collection: 'admin_sessions',
    timestamps: true
});

// Method to validate a token
AdminSchema.statics.validateToken = async function(token) {
    if (!token) return false;
    
    // Find the token in the database
    const admin = await this.findOne({ token });
    return !!admin; // Return true if token exists, false otherwise
};

// Method to create a new admin token
AdminSchema.statics.createToken = async function(adminKey) {
    // Check if the provided key matches the one in .env
    if (adminKey !== process.env.ADMIN_KEY) {
        throw new Error('Invalid admin key');
    }
    
    // Generate a random token
    const token = crypto.randomBytes(32).toString('hex');
    
    // Save the token to the database
    await this.create({ token });
    
    return token;
};

module.exports = mongoose.model('Admin', AdminSchema);
