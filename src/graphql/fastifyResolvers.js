// File: src/graphql/fastifyResolvers.js
// Fastify + Mercurius GraphQL Resolvers
// Optimized for performance with DataLoader and caching

const { ObjectId } = require('mongodb');
const FastifyDataLoaders = require('./dataLoaders');

class FastifyResolvers {
  constructor() {
    this.resolvers = this.createResolvers();
  }

  createResolvers() {
    const resolverInstance = this;

    return {
      Query: {
        // Item resolver with DataLoader optimization
        item: {
          async handler(parent, args, context, info) {
            const { id, type } = args;
            const { db, dataLoaders } = context;

            if (!ObjectId.isValid(id)) {
              throw new Error('Invalid ID format');
            }

            const collectionMap = {
              MOVIE: 'movies',
              SERIES: 'series', 
              ANIME: 'animes',
              LIVETV: 'livetv'
            };

            const collection = collectionMap[type];
            if (!collection) {
              throw new Error('Unsupported item type');
            }

            const loaderName = `${collection.slice(0, -1)}ById`;
            const item = await dataLoaders[loaderName].load(id);
            if (!item) {
              throw new Error('Item not found');
            }

            return {
              ...item,
              __typename: this.getTypeName(collection)
            };
          },
          cache: {
            ttl: 300, // 5 minutes
            key: (parent, args) => `item:${args.type}:${args.id}`
          }
        },

        // Optimized search with DataLoader
        search: {
          async handler(parent, args, context, info) {
            const { query, page = 1, limit = 20 } = args;
            const { dataLoaders } = context;

            const searchQuery = {
              query,
              collections: ['movies', 'series', 'animes', 'livetv'],
              limit
            };

            const items = await dataLoaders.searchResults.load(searchQuery);
            
            return { items };
          },
          cache: {
            ttl: 180, // 3 minutes
            key: (parent, args) => `search:${args.query}:${args.page}:${args.limit}`
          }
        },

        // Movies with trending optimization
        movies: {
          handler: async (parent, args, context, info) => {
            console.log(`🎬 MOVIES RESOLVER CALLED: sort=${args.sort}, page=${args.page}, limit=${args.limit}`);
            const { sort, page = 1, limit = 20 } = args;
            const { db, dataLoaders } = context;

            if (sort === 'TRENDING') {
              console.log(`🚀 MOVIES RESOLVER: Calling getTrendingItems for movies`);
              return await resolverInstance.getTrendingItems('movie', page, limit, dataLoaders);
            }

            const skip = (page - 1) * limit;
            const sortOption = resolverInstance.getSortOption(sort);

            return await db.collection('movies')
              .find({})
              .sort(sortOption)
              .skip(skip)
              .limit(limit)
              .toArray();
          },
          cache: {
            ttl: 300,
            key: (parent, args) => `movies:${args.sort}:${args.page}:${args.limit}`
          }
        },

        // Series resolver
        series: {
          handler: async (parent, args, context, info) => {
            const { sort, page = 1, limit = 20 } = args;
            const { db, dataLoaders } = context;

            if (sort === 'TRENDING') {
              console.log(`🚀 SERIES RESOLVER: Calling getTrendingItems for series`);
              return await resolverInstance.getTrendingItems('tv', page, limit, dataLoaders);
            }

            const skip = (page - 1) * limit;
            const sortOption = resolverInstance.getSortOption(sort);

            return await db.collection('series')
              .find({})
              .sort(sortOption)
              .skip(skip)
              .limit(limit)
              .toArray();
          },
          cache: {
            ttl: 300,
            key: (parent, args) => `series:${args.sort}:${args.page}:${args.limit}`
          }
        },

        // Anime resolver
        anime: {
          handler: async (parent, args, context, info) => {
            const { sort, page = 1, limit = 20 } = args;
            const { db, dataLoaders } = context;

            if (sort === 'TRENDING') {
              console.log(`🚀 ANIME RESOLVER: Calling getTrendingItems for anime`);
              return await resolverInstance.getTrendingItems('tv', page, limit, dataLoaders);
            }

            const skip = (page - 1) * limit;
            const sortOption = resolverInstance.getAnimeSortOption(sort);

            return await db.collection('animes')
              .find({})
              .sort(sortOption)
              .skip(skip)
              .limit(limit)
              .toArray();
          },
          cache: {
            ttl: 300,
            key: (parent, args) => `anime:${args.sort}:${args.page}:${args.limit}`
          }
        },

        // LiveTV resolver
        liveTV: {
          async handler(parent, args, context, info) {
            const { page = 1, limit = 20 } = args;
            const { db } = context;

            const skip = (page - 1) * limit;

            const channels = await db.collection('livetv')
              .find({})
              .sort({ title: 1 })
              .skip(skip)
              .limit(limit)
              .toArray();

            return channels.map(channel => ({
              ...channel,
              __typename: 'LiveTV'
            }));
          },
          cache: {
            ttl: 600, // 10 minutes
            key: (parent, args) => `livetv:${args.page}:${args.limit}`
          }
        },

        // Latest movies with exclusion logic (no cache for real-time updates)
        latestMovies: {
          async handler(parent, args, context, info) {
            const { excludeAncien = true, page = 1, limit = 20 } = args;
            const { db } = context;

            const skip = (page - 1) * limit;
            let query = {};

            if (excludeAncien) {
              query = {
                detailUrl: {
                  $not: { $regex: 'film-ancien', $options: 'i' }
                }
              };
            }

            return await db.collection('movies')
              .find(query)
              .sort({ updatedAt: -1 })
              .skip(skip)
              .limit(limit)
              .toArray();
          }
          // No cache - always fetch fresh data
        },

        // Ancien movies
        ancienMovies: {
          async handler(parent, args, context, info) {
            const { page = 1, limit = 20 } = args;
            const { db } = context;

            const skip = (page - 1) * limit;
            const query = { 
              detailUrl: { $regex: 'film-ancien', $options: 'i' } 
            };

            return await db.collection('movies')
              .find(query)
              .sort({ updatedAt: -1 })
              .skip(skip)
              .limit(limit)
              .toArray();
          },
          cache: {
            ttl: 600,
            key: (parent, args) => `ancienMovies:${args.page}:${args.limit}`
          }
        },

        // Latest series (no cache for real-time updates)
        latestSeries: {
          async handler(parent, args, context, info) {
            const { page = 1, limit = 20 } = args;
            const { db } = context;

            const skip = (page - 1) * limit;

            return await db.collection('series')
              .find({})
              .sort({ updatedAt: -1 })
              .skip(skip)
              .limit(limit)
              .toArray();
          }
          // No cache - always fetch fresh data
        },

        // Latest anime (no cache for real-time updates)
        latestAnime: {
          async handler(parent, args, context, info) {
            const { page = 1, limit = 20 } = args;
            const { db } = context;

            const skip = (page - 1) * limit;

            return await db.collection('animes')
              .find({})
              .sort({ updatedAt: -1 })
              .skip(skip)
              .limit(limit)
              .toArray();
          }
          // No cache - always fetch fresh data
        },

        // Genre-based queries
        moviesByGenre: {
          async handler(parent, args, context, info) {
            const { genre, page = 1, limit = 20 } = args;
            const { dataLoaders } = context;

            const genreQuery = {
              collection: 'movies',
              genre,
              limit
            };

            return await dataLoaders.itemsByGenre.load(genreQuery);
          },
          cache: {
            ttl: 900, // 15 minutes
            key: (parent, args) => `moviesByGenre:${args.genre}:${args.page}:${args.limit}`
          }
        },

        seriesByGenre: {
          async handler(parent, args, context, info) {
            const { genre, page = 1, limit = 20 } = args;
            const { dataLoaders } = context;

            const genreQuery = {
              collection: 'series',
              genre,
              limit
            };

            return await dataLoaders.itemsByGenre.load(genreQuery);
          },
          cache: {
            ttl: 900,
            key: (parent, args) => `seriesByGenre:${args.genre}:${args.page}:${args.limit}`
          }
        },

        animeByGenre: {
          async handler(parent, args, context, info) {
            const { genre, page = 1, limit = 20 } = args;
            const { dataLoaders } = context;

            const genreQuery = {
              collection: 'animes',
              genre,
              limit
            };

            return await dataLoaders.itemsByGenre.load(genreQuery);
          },
          cache: {
            ttl: 900,
            key: (parent, args) => `animeByGenre:${args.genre}:${args.page}:${args.limit}`
          }
        },

        // Available genres
        availableGenres: {
          async handler(parent, args, context, info) {
            const { db } = context;
            const dbService = context.dbService;

            return await dbService.getAvailableGenres();
          },
          cache: {
            ttl: 3600, // 1 hour
            key: () => 'availableGenres'
          }
        },

        // Related seasons
        relatedSeasons: {
          async handler(parent, args, context, info) {
            const { tmdbId, currentItemId } = args;
            const { dataLoaders } = context;

            const relatedItems = await dataLoaders.relatedSeasons.load(tmdbId);

            // Filter out the current item
            return relatedItems.filter(item =>
              item._id.toString() !== currentItemId
            );
          },
          cache: {
            ttl: 1800, // 30 minutes
            key: (parent, args) => `relatedSeasons:${args.tmdbId}:${args.currentItemId}`
          }
        },

        // Database stats for admin
        databaseStats: {
          async handler(parent, args, context, info) {
            const { dbService } = context;
            return await dbService.getDatabaseStats();
          },
          cache: {
            ttl: 300, // 5 minutes
            key: () => 'databaseStats'
          }
        },

        // Stream URL resolver with source fetching
        stream: {
          async handler(parent, args, context, info) {
            const { itemId, type, streamId } = args;
            const { db } = context;
            const logger = context.logger || console;

            // Validate ObjectIds
            if (!ObjectId.isValid(itemId)) {
              throw new Error('Invalid item ID format');
            }
            if (!ObjectId.isValid(streamId)) {
              throw new Error('Invalid stream ID format');
            }

            logger.info(`GraphQL Stream: type=${type}, itemId=${itemId}, streamId=${streamId}`);

            const collectionMap = {
              MOVIE: 'movies',
              SERIES: 'series',
              ANIME: 'animes',
              LIVETV: 'livetv'
            };

            const collection = collectionMap[type];
            if (!collection) {
              throw new Error('Unsupported item type');
            }

            const item = await db.collection(collection).findOne({
              _id: new ObjectId(itemId)
            });

            if (!item) {
              throw new Error('Item not found');
            }

            // Find the specific streaming URL
            let streamingUrl = null;
            let streamParentArrayPath = null;
            let specificEpisodeId = null;

            // Search in direct streaming URLs
            if (item.streamingUrls) {
              streamingUrl = item.streamingUrls.find(url =>
                url._id?.toString() === streamId || url.id === streamId
              );
              if (streamingUrl) {
                streamParentArrayPath = 'streamingUrls';
              }
            }

            // Search in episodes
            if (!streamingUrl && item.episodes) {
              for (const episode of item.episodes) {
                if (episode.streamingUrls) {
                  const found = episode.streamingUrls.find(url =>
                    url._id?.toString() === streamId || url.id === streamId
                  );
                  if (found) {
                    streamingUrl = found;
                    streamParentArrayPath = `episodes.${item.episodes.indexOf(episode)}.streamingUrls`;
                    specificEpisodeId = episode._id;
                    break;
                  }
                }
              }
            }

            if (!streamingUrl) {
              throw new Error('Stream not found');
            }

            // Get current stream data
            let sourceStreamUrl = streamingUrl.sourceStreamUrl;
            let size = streamingUrl.size;
            let streamType = streamingUrl.type;
            let method = streamingUrl.method;
            const lastChecked = streamingUrl.lastChecked ? new Date(streamingUrl.lastChecked).getTime() : 0;
            const fiveMinutesAgo = Date.now() - 5 * 60 * 1000;

            // Fetch/Re-fetch if no source URL or cache is older than 5 minutes
            if (!sourceStreamUrl || lastChecked < fiveMinutesAgo) {
              logger.info(`Fetching/Re-fetching source URL for stream ${streamId} (Provider: ${streamingUrl.provider})`);

              // Import the source fetcher - use the exact same import as the working resolvers.js
              const { fetchSourceStreamUrl } = require('../../utils/sourceStreamFetcher');

              try {
                logger.info(`[FASTIFY DEBUG] Entering try block for fetchSourceStreamUrl`);
                logger.info(`[FASTIFY DEBUG] About to call fetchSourceStreamUrl with URL: ${streamingUrl.url}, Provider: ${streamingUrl.provider}`);
                logger.info(`[FASTIFY DEBUG] fetchSourceStreamUrl function type: ${typeof fetchSourceStreamUrl}`);

                const result = await fetchSourceStreamUrl(streamingUrl.url, streamingUrl.provider);

                logger.info(`[FASTIFY DEBUG] fetchSourceStreamUrl call completed successfully`);
                logger.info(`[GraphQL] fetchSourceStreamUrl completed with result:`, {
                  result: result,
                  hasResult: !!result,
                  url: result?.url,
                  method: result?.method
                });

              if (result && result.url) {
                logger.info(`Fetched source URL: ${result.url}`, {
                  method: result.method,
                });
                sourceStreamUrl = result.url;
                size = result.size;
                streamType = result.type;
                method = result.method;

                const updateSet = { $set: {} };
                const streamObjectId = new ObjectId(streamId); // Ensure streamId is ObjectId
                let arrayFilters = [];
                let updatePathPrefix = "";

                // Determine the correct path prefix and array filters for the update
                if (streamParentArrayPath === "streamingUrls") {
                  updatePathPrefix = "streamingUrls.$[streamElem]";
                  arrayFilters = [{ "streamElem._id": streamObjectId }];
                } else if (
                  streamParentArrayPath === "episodes" &&
                  specificEpisodeId // Ensure we have the episode ID
                ) {
                  updatePathPrefix = "episodes.$[epElem].streamingUrls.$[streamElem]";
                  arrayFilters = [
                    { "epElem._id": specificEpisodeId }, // Use the specific episode's _id
                    { "streamElem._id": streamObjectId },
                  ];
                }

                if (updatePathPrefix) {
                    // Construct the $set object dynamically
                    updateSet.$set[`${updatePathPrefix}.sourceStreamUrl`] = sourceStreamUrl;
                    updateSet.$set[`${updatePathPrefix}.size`] = size;
                    updateSet.$set[`${updatePathPrefix}.type`] = streamType;
                    updateSet.$set[`${updatePathPrefix}.method`] = method;
                    updateSet.$set[`${updatePathPrefix}.lastChecked`] = new Date();

                    // Perform the update operation using the same logic as the working resolvers.js
                    db.collection(collection).updateOne({ _id: new ObjectId(itemId) }, updateSet, { arrayFilters })
                        .then((res) =>
                        logger.info(`DB Update Result (Stream ${streamId}):`, {
                            matched: res.matchedCount,
                            modified: res.modifiedCount,
                        })
                        )
                        .catch((err) =>
                        logger.error(
                            `Failed DB Update (Stream ${streamId}): ${err.message}`
                        )
                        );
                } else {
                     logger.error(`Could not determine update path for stream ${streamId}`);
                }

              } else {
                logger.warn(
                  `Failed fetch for stream ${streamId}. Returning original URL.`
                );
                // Keep original values but maybe default method to iframe if unsure
                sourceStreamUrl = streamingUrl.url;
                method = "iframe"; // Default if fetch fails
              }
              } catch (fetchError) {
                logger.error(`[GraphQL] Error during fetchSourceStreamUrl call:`, {
                  error: fetchError.message,
                  stack: fetchError.stack,
                  code: fetchError.code,
                  streamId: streamId,
                  url: streamingUrl.url,
                  provider: streamingUrl.provider
                });
                // Fallback to original URL
                sourceStreamUrl = streamingUrl.url;
                method = "iframe";
              }
            } else {
              logger.info(`Using cached source URL for stream ${streamId}`);
            }

            return {
              sourceStreamUrl,
              size,
              type: streamType,
              method
            };
          }
        },

        // Play URL resolver
        play: {
          async handler(parent, args, context, info) {
            const { type, id, ep, lang } = args;
            const { db } = context;

            const collectionMap = {
              MOVIE: 'movies',
              SERIES: 'series',
              ANIME: 'animes',
              LIVETV: 'livetv'
            };

            const collection = collectionMap[type];
            if (!collection) {
              throw new Error('Unsupported item type');
            }

            const item = await db.collection(collection).findOne({
              _id: new ObjectId(id)
            });

            if (!item) {
              throw new Error('Item not found');
            }

            let streamingUrl = null;

            // For movies and LiveTV, get direct streaming URL
            if (type === 'MOVIE' || type === 'LIVETV') {
              if (item.streamingUrls && item.streamingUrls.length > 0) {
                // Filter by language if specified
                const filteredUrls = lang ?
                  item.streamingUrls.filter(url => url.language === lang) :
                  item.streamingUrls;

                streamingUrl = filteredUrls[0];
              }
            } else {
              // For series and anime, find the specific episode
              if (item.episodes && ep) {
                const episode = item.episodes.find(e =>
                  e.episodeNumber === ep || e.episodeNumber === ep.toString()
                );

                if (episode && episode.streamingUrls && episode.streamingUrls.length > 0) {
                  const filteredUrls = lang ?
                    episode.streamingUrls.filter(url => url.language === lang) :
                    episode.streamingUrls;

                  streamingUrl = filteredUrls[0];
                }
              }
            }

            if (!streamingUrl) {
              throw new Error('No streaming URL found');
            }

            return {
              url: streamingUrl.url || streamingUrl.sourceStreamUrl
            };
          }
        },

        // Config resolver
        config: {
          async handler(parent, args, context, info) {
            const { db } = context;

            const config = await db.collection('config').findOne({});

            return {
              tmdbApiKey: config?.tmdbApiKey || null,
              wiflixBase: config?.wiflixBase || 'wiflix-max.cam',
              frenchAnimeBase: config?.frenchAnimeBase || 'french-anime.com',
              witvBase: config?.witvBase || 'witv.skin'
            };
          },
          cache: {
            ttl: 1800, // 30 minutes
            key: () => 'config'
          }
        }
      },

      // Interface resolvers
      Item: {
        __resolveType(obj, context, info) {
          if (obj.__typename) {
            return obj.__typename;
          }

          // Fallback type resolution based on object properties
          if (obj.animeLanguage || obj.jikan) {
            return 'Anime';
          }
          if (obj.episodes && obj.episodes.length > 0) {
            return 'Series';
          }
          if (obj.streamingUrls && obj.streamingUrls.length > 0) {
            // Could be Movie or LiveTV, check for other properties
            if (obj.cleanedTitle && !obj.season) {
              return 'Movie';
            }
            return 'LiveTV';
          }

          // Default fallback
          return 'Movie';
        }
      },

      // Mutation resolvers
      Mutation: {
        // Admin login
        adminLogin: {
          async handler(parent, args, context, info) {
            const { adminKey } = args;
            const { db } = context;

            const expectedKey = process.env.ADMIN_KEY;
            if (!expectedKey || adminKey !== expectedKey) {
              return {
                success: false,
                message: 'Invalid admin key'
              };
            }

            // Generate a simple token (in production, use JWT)
            const token = Buffer.from(`${adminKey}:${Date.now()}`).toString('base64');

            // Store token in database with expiration
            await db.collection('admin').updateOne(
              { type: 'session' },
              {
                $set: {
                  token,
                  createdAt: new Date(),
                  expiresAt: new Date(Date.now() + 24 * 60 * 60 * 1000) // 24 hours
                }
              },
              { upsert: true }
            );

            return {
              success: true,
              token,
              message: 'Login successful'
            };
          }
        },

        // Validate admin token
        validateAdminToken: {
          async handler(parent, args, context, info) {
            const { token } = args;
            const { db } = context;

            const session = await db.collection('admin').findOne({
              type: 'session',
              token,
              expiresAt: { $gt: new Date() }
            });

            return {
              isValid: !!session
            };
          }
        }
      }
    };
  }

  // Helper methods
  async getTrendingItems(mediaType, page, limit, dataLoaders) {
    console.log(`🔥 TRENDING: Getting trending ${mediaType} items (page=${page}, limit=${limit})`);

    try {
      const skip = (page - 1) * limit;

      // Query trendingitems collection directly
      const trendingItems = await this.db.collection('trendingitems')
        .find({ mediaType })
        .sort({ rank: 1 })
        .skip(skip)
        .limit(limit)
        .toArray();

      console.log(`📊 TRENDING: Found ${trendingItems.length} trending items:`, trendingItems.map(t => `${t.tmdbId}(rank:${t.rank})`));

      if (trendingItems.length === 0) {
        console.log(`⚠️ TRENDING: No trending items found, falling back to latest`);
        const collection = mediaType === 'movie' ? 'movies' :
                          mediaType === 'tv' ? 'series' : 'animes';

        return await this.db.collection(collection)
          .find({})
          .sort({ updatedAt: -1 })
          .skip(skip)
          .limit(limit)
          .toArray();
      }

      // Get the actual media items
      const tmdbIds = trendingItems.map(item => item.tmdbId);
      const collectionName = mediaType === 'movie' ? 'movies' :
                            mediaType === 'tv' ? 'series' : 'animes';

      console.log(`🎯 TRENDING: Looking in ${collectionName} for TMDB IDs:`, tmdbIds);

      const mediaItems = await this.db.collection(collectionName)
        .find({ 'tmdb.id': { $in: tmdbIds } })
        .toArray();

      console.log(`📦 TRENDING: Found ${mediaItems.length} media items`);

      // Create rank map and add trendingRank to items
      const rankMap = {};
      trendingItems.forEach(item => {
        rankMap[item.tmdbId] = item.rank;
      });

      const result = mediaItems
        .map(item => ({
          ...item,
          trendingRank: rankMap[item.tmdb?.id] || null
        }))
        .sort((a, b) => (a.trendingRank || 999) - (b.trendingRank || 999));

      console.log(`✅ TRENDING: Returning ${result.length} items with ranks:`, result.map(r => `${r.title}(${r.trendingRank})`));
      return result;

    } catch (error) {
      console.error(`❌ TRENDING ERROR:`, error);
      // Fallback to latest
      const collection = mediaType === 'movie' ? 'movies' :
                        mediaType === 'tv' ? 'series' : 'animes';
      const skip = (page - 1) * limit;

      return await this.db.collection(collection)
        .find({})
        .sort({ updatedAt: -1 })
        .skip(skip)
        .limit(limit)
        .toArray();
    }
  }

  getSortOption(sort) {
    switch (sort) {
      case 'LATEST':
        return { updatedAt: -1 };
      case 'ALPHA':
        return { title: 1 };
      case 'RELEASE':
        return { 'tmdb.release_date': -1, createdAt: -1 };
      default:
        return { updatedAt: -1 };
    }
  }

  getAnimeSortOption(sort) {
    switch (sort) {
      case 'LATEST':
        return { updatedAt: -1 };
      case 'ALPHA':
        return { title: 1 };
      case 'RELEASE':
        return { 
          'jikan.year': -1, 
          'tmdb.release_date': -1, 
          createdAt: -1 
        };
      default:
        return { updatedAt: -1 };
    }
  }

  getTypeName(collection) {
    const typeMap = {
      movies: 'Movie',
      series: 'Series',
      animes: 'Anime',
      livetv: 'LiveTV'
    };
    return typeMap[collection] || 'Unknown';
  }
}

module.exports = FastifyResolvers;
