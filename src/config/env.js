const path = require('path');
require('dotenv').config({ path: path.join(__dirname, '..', '..', '.env') });
const constants = require('./constants');

// Function to sanitize Redis host for Render.com and other cloud providers
const getRedisHost = () => {
  let redisHost = process.env.REDIS_HOST;

  if (!redisHost) {
    if (process.env.NODE_ENV === 'production') {
      // Render.com provides REDIS_URL, fallback to other common env vars
      redisHost = process.env.REDIS_URL ||
                  process.env.REDIS_SERVICE_URL ||
                  process.env.REDISCLOUD_URL ||
                  'localhost';
    } else {
      redisHost = 'localhost';
    }
  }

  // Sanitize the host and remove any protocol
  if (redisHost.includes('://')) {
    const parsed = redisHost.split('://')[1];
    // Handle URLs with auth: redis://user:pass@host:port
    if (parsed.includes('@')) {
      return parsed.split('@')[1].split('/')[0].split(':')[0];
    }
    return parsed.split('/')[0].split(':')[0];
  }

  return redisHost;
}

// Function to get Redis password from URL or env
const getRedisPassword = () => {
  if (process.env.REDIS_PASSWORD) {
    return process.env.REDIS_PASSWORD;
  }

  // Extract password from Redis URL if present
  const redisUrl = process.env.REDIS_URL || process.env.REDISCLOUD_URL;
  if (redisUrl && redisUrl.includes('://') && redisUrl.includes('@')) {
    const auth = redisUrl.split('://')[1].split('@')[0];
    if (auth.includes(':')) {
      return auth.split(':')[1];
    }
  }

  return undefined;
}

// Function to get Redis port from URL or env
const getRedisPort = () => {
  if (process.env.REDIS_PORT) {
    return parseInt(process.env.REDIS_PORT);
  }

  // Extract port from Redis URL if present
  const redisUrl = process.env.REDIS_URL || process.env.REDISCLOUD_URL;
  if (redisUrl && redisUrl.includes('://')) {
    const hostPart = redisUrl.split('://')[1];
    const afterAuth = hostPart.includes('@') ? hostPart.split('@')[1] : hostPart;
    const portPart = afterAuth.split('/')[0];

    if (portPart.includes(':')) {
      return parseInt(portPart.split(':')[1]);
    }
  }

  return 6379; // Default Redis port
}

module.exports = {
  // Database
  mongoUri: process.env.MONGO_URI,

  // Server configuration
  port: process.env.FASTIFY_PORT || process.env.PORT || 3001,
  host: process.env.HOST || '0.0.0.0', // Render.com requires 0.0.0.0

  // API Keys
  tmdbApiKey: process.env.TMDB_API_KEY,
  geminiApiKey: process.env.GEMINI_API_KEY,
  telegramToken: process.env.TELEGRAM_TOKEN,

  // Application settings
  scrapeMode: process.env.SCRAPE_MODE || 'full',

  // Redis configuration (enhanced for cloud deployment)
  redisHost: getRedisHost(),
  redisPort: getRedisPort(),
  redisPassword: getRedisPassword(),
  redisUrl: process.env.REDIS_URL || process.env.REDISCLOUD_URL,

  // Render.com specific
  isRender: !!process.env.RENDER,
  renderServiceName: process.env.RENDER_SERVICE_NAME,

  // CORS and frontend configuration
  frontendUrl: process.env.FRONTEND_URL || process.env.NEXT_PUBLIC_FRONTEND_URL,
  corsOrigins: process.env.CORS_ORIGINS,
  customDomain: process.env.CUSTOM_DOMAIN,

  // Performance settings for cloud deployment
  maxConcurrentPages: parseInt(process.env.MAX_CONCURRENT_PAGES) || 3,
  enableCaching: process.env.ENABLE_CACHING !== 'false',

  ...constants,
};