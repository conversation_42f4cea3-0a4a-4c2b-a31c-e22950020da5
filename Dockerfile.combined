# Combined Frontend + Backend Dockerfile for Render.com
FROM node:18-slim AS base

# Install system dependencies for Puppeteer
RUN apt-get update && apt-get install -yq --no-install-recommends \
    chromium \
    libasound2 \
    libatk-bridge2.0-0 \
    libatk1.0-0 \
    libcairo2 \
    libcups2 \
    libdbus-1-3 \
    libexpat1 \
    libfontconfig1 \
    libgbm-dev \
    libgcc1 \
    libgconf-2-4 \
    libgdk-pixbuf2.0-0 \
    libglib2.0-0 \
    libgtk-3-0 \
    libnspr4 \
    libnss3 \
    libpango-1.0-0 \
    libpangocairo-1.0-0 \
    libstdc++6 \
    libx11-6 \
    libx11-xcb1 \
    libxcb1 \
    libxcomposite1 \
    libxcursor1 \
    libxdamage1 \
    libxext6 \
    libxfixes3 \
    libxi6 \
    libxrandr2 \
    libxrender1 \
    libxshmfence-dev \
    libxtst6 \
    ca-certificates \
    fonts-liberation \
    lsb-release \
    wget \
    xdg-utils \
    curl \
    && apt-get clean \
    && rm -rf /var/lib/apt/lists/*

WORKDIR /app

# Build Frontend First
FROM base AS frontend-builder
COPY netstream-nextjs/package*.json ./netstream-nextjs/
WORKDIR /app/netstream-nextjs
RUN npm ci

COPY netstream-nextjs/ ./
ENV SKIP_BUILD_STATIC_GENERATION=true
ENV NEXT_TELEMETRY_DISABLED=1
# For static export, we don't need database connections during build
# The frontend will connect to the backend at runtime
RUN npm run build

# Backend Dependencies
FROM base AS backend-deps
COPY package*.json ./
RUN npm ci --only=production --no-audit --no-fund && npm cache clean --force

# Final Combined Image
FROM base AS production
WORKDIR /app

# Copy backend dependencies and code
COPY --from=backend-deps /app/node_modules ./node_modules
COPY . .

# Copy built frontend (static export)
COPY --from=frontend-builder /app/netstream-nextjs/out ./netstream-nextjs/out
COPY --from=frontend-builder /app/netstream-nextjs/public ./netstream-nextjs/public

# Set environment variables
ENV NODE_ENV=production
ENV PORT=3001
ENV HOST=0.0.0.0
ENV PUPPETEER_EXECUTABLE_PATH=/usr/bin/chromium
ENV PUPPETEER_ARGS="--no-sandbox --disable-setuid-sandbox --disable-dev-shm-usage"

# Create non-root user
RUN groupadd -r netstream && useradd -r -g netstream netstream
RUN chown -R netstream:netstream /app
USER netstream

EXPOSE 3001

# Health check
HEALTHCHECK --interval=30s --timeout=10s --start-period=60s --retries=3 \
  CMD curl -f http://localhost:${PORT}/health || exit 1

# Start combined server
CMD ["node", "server-combined.js"]
