# Render.com Free Tier Optimization Plan

## Render.com Free Tier Limitations

### Free Tier Specifications
- **Memory**: 512MB RAM
- **CPU**: 0.1 vCPU (shared)
- **Storage**: 1GB SSD
- **Bandwidth**: 100GB/month
- **Sleep**: Services sleep after 15 minutes of inactivity
- **Build Time**: 15 minutes maximum
- **Concurrent Builds**: 1

### Current Application Resource Usage
- **Backend Memory**: ~300MB (needs reduction to ~400MB max)
- **Frontend Memory**: ~150MB (acceptable)
- **Build Time**: ~8-12 minutes (acceptable)
- **Dependencies**: Heavy (needs optimization)

## Optimization Strategy

### Phase 1: Memory Optimization (Critical)

#### 1. Reduce Puppeteer Memory Usage
```javascript
// Current: Multiple browser instances
// Optimized: Single browser with page pooling
class OptimizedBrowserManager {
  constructor() {
    this.browser = null;
    this.pagePool = [];
    this.maxPages = 2; // Reduced from unlimited
  }

  async getBrowser() {
    if (!this.browser) {
      this.browser = await puppeteer.launch({
        headless: 'new',
        args: [
          '--no-sandbox',
          '--disable-setuid-sandbox',
          '--disable-dev-shm-usage',
          '--disable-gpu',
          '--memory-pressure-off',
          '--max_old_space_size=256', // Limit Node.js heap
          '--disable-background-timer-throttling',
          '--disable-backgrounding-occluded-windows',
          '--disable-renderer-backgrounding'
        ]
      });
    }
    return this.browser;
  }

  async getPage() {
    if (this.pagePool.length > 0) {
      return this.pagePool.pop();
    }
    
    const browser = await this.getBrowser();
    const page = await browser.newPage();
    
    // Optimize page for memory
    await page.setViewport({ width: 1280, height: 720 });
    await page.setUserAgent('Mozilla/5.0 (compatible; NetStreamBot/1.0)');
    
    return page;
  }

  async releasePage(page) {
    if (this.pagePool.length < this.maxPages) {
      await page.goto('about:blank');
      this.pagePool.push(page);
    } else {
      await page.close();
    }
  }
}
```

#### 2. Optimize Cache Memory Usage
```javascript
// Reduce cache sizes for free tier
const FREE_TIER_CACHE_CONFIG = {
  maxSize: 500, // Reduced from 10000
  maxMemoryMB: 50, // Reduced from 100MB
  ttl: 1800000, // 30 minutes instead of 1 hour
  
  // Separate cache limits by type
  caches: {
    graphql: { maxSize: 100, ttl: 900000 }, // 15 minutes
    tmdb: { maxSize: 200, ttl: 3600000 }, // 1 hour
    jikan: { maxSize: 100, ttl: 3600000 }, // 1 hour
    gemini: { maxSize: 50, ttl: 7200000 }, // 2 hours
    enrichment: { maxSize: 50, ttl: 3600000 }, // 1 hour
    media: { maxSize: 100, ttl: 1800000 }, // 30 minutes
    streaming: { maxSize: 50, ttl: 600000 } // 10 minutes
  }
};
```

#### 3. Disable Heavy Features for Free Tier
```javascript
// Environment-based feature flags
const FREE_TIER_CONFIG = {
  ENABLE_SCRAPING: false, // Disable scraping on free tier
  ENABLE_BACKGROUND_JOBS: false, // Disable Bull queues
  ENABLE_ADVANCED_ENRICHMENT: false, // Disable Gemini AI
  MAX_CONCURRENT_REQUESTS: 5, // Limit concurrent requests
  ENABLE_PUPPETEER: false, // Disable Puppeteer completely
  CACHE_STRATEGY: 'minimal' // Use minimal caching
};

// Feature flag middleware
const featureFlag = (feature) => {
  return (req, res, next) => {
    if (process.env.RENDER_FREE_TIER === 'true' && !FREE_TIER_CONFIG[feature]) {
      return res.status(503).json({ 
        error: 'Feature not available on free tier',
        upgrade: 'Please upgrade to access this feature'
      });
    }
    next();
  };
};
```

### Phase 2: Dependency Optimization

#### 1. Remove Heavy Dependencies
```json
{
  "dependencies": {
    // Remove these for free tier
    // "puppeteer": "^24.8.2", // 200MB+ - REMOVED
    // "bull": "^4.16.5", // Heavy job queue - REMOVED  
    // "@google/generative-ai": "^0.6.0", // AI features - REMOVED
    // "canvas": "^3.1.0", // Heavy graphics - REMOVED
    // "chart.js": "^4.4.1", // Charts - REMOVED
    
    // Keep essential dependencies
    "fastify": "^4.26.2",
    "mercurius": "^13.3.3", 
    "mongodb": "^6.3.0",
    "ioredis": "^5.6.1",
    "axios": "^1.8.4",
    "graphql": "^16.8.1",
    "dotenv": "^16.5.0",
    "pino": "^8.19.0"
  }
}
```

#### 2. Lightweight Alternatives
```javascript
// Replace heavy scraping with API-only approach
const lightweightContentFetcher = {
  // Use only TMDB API for content
  async fetchMovies(page = 1) {
    const response = await axios.get(`https://api.themoviedb.org/3/discover/movie`, {
      params: {
        api_key: process.env.TMDB_API_KEY,
        page,
        sort_by: 'popularity.desc'
      }
    });
    return response.data.results;
  },

  // Use only Jikan API for anime
  async fetchAnime(page = 1) {
    const response = await axios.get(`https://api.jikan.moe/v4/top/anime`, {
      params: { page }
    });
    return response.data.data;
  }
};
```

### Phase 3: Service Architecture for Free Tier

#### 1. Single Service Deployment
```yaml
# Instead of 3 services (backend + frontend + redis)
# Deploy as single service to stay within free tier

services:
  netstream-free:
    type: Web Service
    plan: Free
    environment: Docker
    build_command: docker build -f Dockerfile.free .
    start_command: node server-free.js
```

#### 2. Embedded Frontend
```javascript
// Serve frontend from backend to reduce services
const path = require('path');

// Serve Next.js static build from backend
fastify.register(require('@fastify/static'), {
  root: path.join(__dirname, 'netstream-nextjs/out'),
  prefix: '/',
  decorateReply: false
});

// API routes
fastify.register(async function (fastify) {
  fastify.get('/api/*', async (request, reply) => {
    // Handle API requests
  });
});

// Catch-all for frontend routes
fastify.get('*', async (request, reply) => {
  return reply.sendFile('index.html');
});
```

#### 3. In-Memory Cache Instead of Redis
```javascript
// Replace Redis with in-memory cache for free tier
class InMemoryCache {
  constructor() {
    this.cache = new Map();
    this.maxSize = 200; // Very limited for free tier
    this.cleanup();
  }

  set(key, value, ttl = 1800000) { // 30 minutes default
    if (this.cache.size >= this.maxSize) {
      this.evictOldest();
    }
    
    this.cache.set(key, {
      value,
      expires: Date.now() + ttl
    });
  }

  get(key) {
    const item = this.cache.get(key);
    if (!item) return null;
    
    if (Date.now() > item.expires) {
      this.cache.delete(key);
      return null;
    }
    
    return item.value;
  }

  cleanup() {
    setInterval(() => {
      const now = Date.now();
      for (const [key, item] of this.cache.entries()) {
        if (now > item.expires) {
          this.cache.delete(key);
        }
      }
    }, 300000); // Clean every 5 minutes
  }

  evictOldest() {
    const firstKey = this.cache.keys().next().value;
    if (firstKey) {
      this.cache.delete(firstKey);
    }
  }
}
```

### Phase 4: Database Optimization

#### 1. Lightweight Database Operations
```javascript
// Optimize queries for free tier
const optimizedQueries = {
  // Limit all queries
  async findMovies(page = 1, limit = 10) { // Reduced from 20
    return await db.collection('movies')
      .find({}, { 
        projection: { 
          title: 1, 
          poster: 1, 
          year: 1, 
          _id: 1 
        } // Only essential fields
      })
      .sort({ updatedAt: -1 })
      .skip((page - 1) * limit)
      .limit(limit)
      .toArray();
  },

  // Simplified search
  async searchContent(query, limit = 5) { // Very limited results
    return await db.collection('movies')
      .find(
        { $text: { $search: query } },
        { projection: { title: 1, poster: 1, year: 1, _id: 1 } }
      )
      .limit(limit)
      .toArray();
  }
};
```

### Phase 5: Frontend Optimization for Free Tier

#### 1. Static Export Configuration
```javascript
// next.config.mjs for free tier
const nextConfig = {
  output: 'export', // Static export instead of standalone
  trailingSlash: true,
  images: {
    unoptimized: true
  },
  experimental: {
    optimizePackageImports: ['@heroicons/react']
  }
};
```

#### 2. Minimal Feature Set
```javascript
// Disable heavy features in frontend
const FREE_TIER_FEATURES = {
  ENABLE_VIDEO_PLAYER: false, // Disable video streaming
  ENABLE_ADMIN_PANEL: false, // Disable admin features
  ENABLE_REAL_TIME_SEARCH: false, // Disable real-time search
  ENABLE_INFINITE_SCROLL: false, // Use pagination only
  MAX_ITEMS_PER_PAGE: 10 // Reduce from 20
};
```

## Implementation Plan

### Step 1: Create Free Tier Dockerfile
```dockerfile
# Dockerfile.free - Optimized for free tier
FROM node:18-alpine AS builder

WORKDIR /app

# Copy package files
COPY package.json package-lock.json ./

# Install only production dependencies
RUN npm ci --only=production --no-audit --no-fund

# Copy source code
COPY . .

# Build frontend for static export
WORKDIR /app/netstream-nextjs
RUN npm ci --only=production
RUN npm run build

# Production stage
FROM node:18-alpine AS runtime

WORKDIR /app

# Copy backend
COPY --from=builder /app/node_modules ./node_modules
COPY --from=builder /app/src ./src
COPY --from=builder /app/server-free.js ./
COPY --from=builder /app/package.json ./

# Copy frontend build
COPY --from=builder /app/netstream-nextjs/out ./public

EXPOSE 3000

# Health check
HEALTHCHECK --interval=30s --timeout=10s --start-period=60s --retries=3 \
  CMD curl -f http://localhost:3000/health || exit 1

CMD ["node", "server-free.js"]
```

### Step 2: Create Free Tier Server
```javascript
// server-free.js - Optimized server for free tier
require('dotenv').config();

const fastify = require('fastify')({
  logger: {
    level: 'warn' // Reduce logging
  },
  bodyLimit: 1048576, // 1MB limit
  connectionTimeout: 10000,
  keepAliveTimeout: 5000
});

// Minimal middleware
fastify.register(require('@fastify/cors'), {
  origin: true
});

fastify.register(require('@fastify/static'), {
  root: path.join(__dirname, 'public'),
  prefix: '/'
});

// Basic GraphQL with minimal features
fastify.register(require('mercurius'), {
  schema: minimalSchema,
  resolvers: minimalResolvers,
  graphiql: false // Disable GraphiQL in production
});

// Health check
fastify.get('/health', async (request, reply) => {
  return { status: 'ok', memory: process.memoryUsage() };
});

// Start server
const start = async () => {
  try {
    await fastify.listen({ port: process.env.PORT || 3000, host: '0.0.0.0' });
    console.log('NetStream Free Tier running on port', process.env.PORT || 3000);
  } catch (err) {
    fastify.log.error(err);
    process.exit(1);
  }
};

start();
```

### Step 3: Environment Configuration
```bash
# .env.free - Free tier environment
NODE_ENV=production
PORT=3000

# Database (keep existing)
MONGO_URI=your_mongodb_uri

# Feature flags for free tier
RENDER_FREE_TIER=true
ENABLE_SCRAPING=false
ENABLE_BACKGROUND_JOBS=false
ENABLE_ADVANCED_ENRICHMENT=false
ENABLE_PUPPETEER=false

# API keys (optional for free tier)
TMDB_API_KEY=your_tmdb_key

# Cache settings
CACHE_STRATEGY=minimal
MAX_CACHE_SIZE=200
CACHE_TTL=1800000

# Performance limits
MAX_CONCURRENT_REQUESTS=5
MAX_ITEMS_PER_PAGE=10
```

## Expected Results

### Memory Usage Reduction
- **Before**: ~300MB
- **After**: ~150MB (50% reduction)

### Feature Limitations
- ❌ Content scraping disabled
- ❌ Background jobs disabled  
- ❌ AI enrichment disabled
- ❌ Video streaming disabled
- ❌ Admin panel disabled
- ✅ Basic content browsing
- ✅ Search functionality
- ✅ User authentication
- ✅ Content metadata display

### Performance Expectations
- **Cold start**: 30-45 seconds (free tier limitation)
- **Response time**: 200-500ms
- **Concurrent users**: 10-20 maximum
- **Uptime**: Subject to sleep after 15 minutes inactivity

This optimization plan will allow the NetStream application to run on Render.com's free tier while maintaining core functionality for demonstration and testing purposes.
