/** @type {import('next').NextConfig} */
const nextConfig = {
  // For combined server deployment, we need static export
  // This creates static HTML files that can be served by our Fastify server
  output: 'export',
  trailingSlash: true,
  skipTrailingSlashRedirect: true,

  // Optimize images
  images: {
    unoptimized: true, // For static export compatibility
  },

  // Skip build-time data fetching to prevent API connection issues during Docker build

  // Environment variables for build time
  env: {
    SKIP_BUILD_STATIC_GENERATION: 'true',
  },

  // Webpack optimizations
  webpack: (config, { dev, isServer }) => {
    if (!dev && !isServer) {
      // Optimize bundle size
      config.optimization.splitChunks = {
        chunks: 'all',
        cacheGroups: {
          vendor: {
            test: /[\\/]node_modules[\\/]/,
            name: 'vendors',
            chunks: 'all',
          },
        },
      };
    }
    return config;
  },

  // Disable static generation for pages that require API calls
  experimental: {
    missingSuspenseWithCSRBailout: false,
  },
};

export default nextConfig;
