/** @type {import('next').NextConfig} */
const nextConfig = {
  // This enables the standalone output mode, which creates a
  // minimal server for production deployment in Docker.
  output: 'standalone',

  // Optimize images
  images: {
    unoptimized: true, // For static export compatibility
  },

  // Skip build-time data fetching to prevent API connection issues during Docker build
  trailingSlash: false,

  // Environment variables for build time
  env: {
    SKIP_BUILD_STATIC_GENERATION: 'true',
  },

  // Webpack optimizations
  webpack: (config, { dev, isServer }) => {
    if (!dev && !isServer) {
      // Optimize bundle size
      config.optimization.splitChunks = {
        chunks: 'all',
        cacheGroups: {
          vendor: {
            test: /[\\/]node_modules[\\/]/,
            name: 'vendors',
            chunks: 'all',
          },
        },
      };
    }
    return config;
  },

  // Disable static generation for pages that require API calls
  experimental: {
    missingSuspenseWithCSRBailout: false,
  },
};

export default nextConfig;
