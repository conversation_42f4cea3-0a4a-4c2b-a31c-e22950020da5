/** @type {import('next').NextConfig} */
const nextConfig = {
  // This enables the standalone output mode, which creates a
  // minimal server for production deployment in Docker.
  output: 'standalone',

  // Optimize images
  images: {
    unoptimized: true, // For static export compatibility
  },

  // Webpack optimizations
  webpack: (config, { dev, isServer }) => {
    if (!dev && !isServer) {
      // Optimize bundle size
      config.optimization.splitChunks = {
        chunks: 'all',
        cacheGroups: {
          vendor: {
            test: /[\\/]node_modules[\\/]/,
            name: 'vendors',
            chunks: 'all',
          },
        },
      };
    }
    return config;
  },
};

export default nextConfig;
