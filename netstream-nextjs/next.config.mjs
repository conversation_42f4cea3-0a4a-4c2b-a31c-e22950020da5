/** @type {import('next').NextConfig} */
const nextConfig = {
  // Use export for free tier (static files served by backend)
  output: process.env.RENDER_FREE_TIER === 'true' ? 'export' : 'standalone',

  // Experimental features
  experimental: {
    outputFileTracingRoot: undefined,
    optimizePackageImports: ['@heroicons/react', 'framer-motion']
  },

  // Disable telemetry
  telemetry: false,

  // Optimize images
  images: {
    unoptimized: true, // For static export compatibility
  },

  // Free tier optimizations
  ...(process.env.RENDER_FREE_TIER === 'true' && {
    trailingSlash: true,
    distDir: 'out',
    generateEtags: false,
    compress: false, // Let backend handle compression
    poweredByHeader: false,
    reactStrictMode: true
  }),

  // Webpack optimizations for smaller bundle
  webpack: (config, { dev, isServer }) => {
    if (!dev && !isServer) {
      // Optimize bundle size for free tier
      config.optimization.splitChunks = {
        chunks: 'all',
        cacheGroups: {
          vendor: {
            test: /[\\/]node_modules[\\/]/,
            name: 'vendors',
            chunks: 'all',
            maxSize: 200000, // 200KB chunks max
          },
        },
      };
    }
    return config;
  },
};

export default nextConfig;
