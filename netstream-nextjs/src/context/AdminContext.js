'use client'
import { createContext, useContext, useState, useEffect } from 'react'
import { useLazyQuery } from '@apollo/client'
import { gql } from '@apollo/client'

const AdminContext = createContext()

// Query to validate admin token
const VALIDATE_ADMIN_TOKEN = gql`
  query ValidateAdminToken($token: String!) {
    validateAdminToken(token: $token) {
      isValid
    }
  }
`

export function AdminProvider({ children }) {
  const [isAdmin, setIsAdmin] = useState(false)
  const [adminToken, setAdminToken] = useState(null)
  const [isLoginModalOpen, setIsLoginModalOpen] = useState(false)
  const [isAdminPanelOpen, setIsAdminPanelOpen] = useState(false)
  const [validateToken] = useLazyQuery(VALIDATE_ADMIN_TOKEN)

  // Function to validate token
  const checkTokenValidity = async (token) => {
    try {
      const { data } = await validateToken({ variables: { token } })
      return data?.validateAdminToken?.isValid || false
    } catch (error) {
      console.error('Token validation error:', error)
      return false
    }
  }

  useEffect(() => {
    // Check for existing admin token
    const initializeAuth = async () => {
      if (typeof window !== 'undefined') {
        const token = localStorage.getItem('adminToken')
        console.log('AdminContext: useEffect - token from localStorage:', token)
        if (token) {
          // Validate the token before using it
          const isValid = await checkTokenValidity(token)
          console.log('AdminContext: token validation result:', isValid)
          if (isValid) {
            setAdminToken(token)
            setIsAdmin(true)
            console.log('AdminContext: valid token loaded from localStorage')
          } else {
            // Remove invalid token
            localStorage.removeItem('adminToken')
            console.log('AdminContext: invalid token removed from localStorage')
          }
        }
      }
    }

    initializeAuth()
  }, [validateToken])

  const login = async (token) => {
    console.log('AdminContext: login called with token:', token)

    if (!token) {
      console.error('AdminContext: no token provided to login function')
      return false
    }

    // Validate the token before storing it
    const isValid = await checkTokenValidity(token)
    console.log('AdminContext: login token validation result:', isValid)

    if (isValid) {
      setAdminToken(token)
      setIsAdmin(true)
      if (typeof window !== 'undefined') {
        localStorage.setItem('adminToken', token)
        console.log('AdminContext: valid token stored in localStorage')
      }
      setIsLoginModalOpen(false)
      return true
    } else {
      console.error('AdminContext: invalid token provided to login function')
      return false
    }
  }

  const logout = () => {
    setAdminToken(null)
    setIsAdmin(false)
    if (typeof window !== 'undefined') {
      localStorage.removeItem('adminToken')
    }
    setIsAdminPanelOpen(false)
  }

  const showLoginModal = () => {
    setIsLoginModalOpen(true)
  }

  const hideLoginModal = () => {
    setIsLoginModalOpen(false)
  }

  const showAdminPanel = () => {
    setIsAdminPanelOpen(true)
  }

  const hideAdminPanel = () => {
    setIsAdminPanelOpen(false)
  }

  const value = {
    isAdmin,
    user: adminToken ? { token: adminToken } : null,
    isLoginModalOpen,
    isAdminPanelOpen,
    login,
    logout,
    showLoginModal,
    hideLoginModal,
    showAdminPanel,
    hideAdminPanel
  }

  console.log('AdminContext: current state - isAdmin:', isAdmin, 'adminToken:', adminToken, 'user:', value.user)

  return (
    <AdminContext.Provider value={value}>
      {children}
    </AdminContext.Provider>
  )
}

export function useAdminContext() {
  const context = useContext(AdminContext)
  if (context === undefined) {
    throw new Error('useAdminContext must be used within an AdminProvider')
  }
  return context
}
