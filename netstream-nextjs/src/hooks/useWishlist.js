'use client'
import { useState, useEffect, useCallback } from 'react'
import toast from 'react-hot-toast'

export function useWishlist() {
  const [wishlistItems, setWishlistItems] = useState([])
  const [groupedWishlist, setGroupedWishlist] = useState({
    movies: [],
    series: [],
    anime: []
  })

  // Load wishlist from localStorage on mount
  useEffect(() => {
    if (typeof window !== 'undefined') {
      const saved = localStorage.getItem('wishlist')
      if (saved) {
        try {
          const items = JSON.parse(saved)
          setWishlistItems(items)
          groupItems(items)
        } catch (error) {
          console.error('Error loading wishlist:', error)
        }
      }
    }
  }, [])

  const groupItems = useCallback((items) => {
    const grouped = {
      movies: items.filter(item => {
        // Handle both "movie" and "movies" types like series and anime
        return item.type === 'movie' || item.type === 'movies'
      }),
      series: items.filter(item => item.type === 'series'),
      anime: items.filter(item => item.type === 'anime')
    }

    console.log('🔄 Wishlist: Grouped result:', {
      movies: grouped.movies.length,
      series: grouped.series.length,
      anime: grouped.anime.length
    })

    setGroupedWishlist(grouped)
  }, [])

  const saveToStorage = useCallback((items) => {
    if (typeof window !== 'undefined') {
      localStorage.setItem('wishlist', JSON.stringify(items))
    }
  }, [])

  const addToWishlist = useCallback(async (item) => {
    try {
      // Check if item already exists
      const existingIndex = wishlistItems.findIndex(existing =>
        existing.id === item.id && existing.type === item.type
      )

      if (existingIndex !== -1) {
        toast.info(`"${item.title || item.displayTitle}" is already in wishlist`)
        return
      }

      const newItem = {
        id: item.id,
        title: item.title || item.displayTitle,
        thumbnail: item.thumbnail || item.image,
        type: item.type,
        year: item.year || item.metadata?.year,
        addedAt: new Date().toISOString()
      }

      setWishlistItems(prev => {
        // Double-check for duplicates in the current state
        const alreadyExists = prev.some(existing =>
          existing.id === newItem.id && existing.type === newItem.type
        )

        if (alreadyExists) {
          return prev
        }

        const updated = [...prev, newItem]

        // Save and group immediately
        saveToStorage(updated)
        groupItems(updated)
        return updated
      })

      toast.success(`Added "${newItem.title}" to wishlist`)
    } catch (error) {
      console.error('Error adding to wishlist:', error)
      toast.error('Failed to add to wishlist')
    }
  }, [wishlistItems, saveToStorage, groupItems])

  const removeFromWishlist = useCallback(async (id, type) => {
    try {
      setWishlistItems(prev => {
        const updated = prev.filter(item => !(item.id === id && item.type === type))
        saveToStorage(updated)
        groupItems(updated)
        return updated
      })

      toast.success('Removed from wishlist')
    } catch (error) {
      console.error('Error removing from wishlist:', error)
      toast.error('Failed to remove from wishlist')
    }
  }, [saveToStorage, groupItems])

  const isInWishlist = useCallback((id, type) => {
    return wishlistItems.some(item => item.id === id && item.type === type)
  }, [wishlistItems])

  const clearWishlist = useCallback(() => {
    setWishlistItems([])
    setGroupedWishlist({ movies: [], series: [], anime: [] })
    if (typeof window !== 'undefined') {
      localStorage.removeItem('wishlist')
    }
    toast.success('Wishlist cleared')
  }, [])

  const debugWishlist = useCallback(() => {
    console.log('🔍 Wishlist Debug Info:')
    console.log('  State wishlistItems:', wishlistItems)
    console.log('  State groupedWishlist:', groupedWishlist)

    if (typeof window !== 'undefined') {
      const saved = localStorage.getItem('wishlist')
      console.log('  localStorage raw:', saved)
      if (saved) {
        try {
          const parsed = JSON.parse(saved)
          console.log('  localStorage parsed:', parsed)
        } catch (error) {
          console.log('  localStorage parse error:', error)
        }
      }
    }
  }, [wishlistItems, groupedWishlist])

  return {
    wishlistItems,
    groupedWishlist,
    addToWishlist,
    removeFromWishlist,
    isInWishlist,
    clearWishlist,
    debugWishlist
  }
}
