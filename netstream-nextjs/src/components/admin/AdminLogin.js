'use client'
import { useState } from 'react'
import { useMutation } from '@apollo/client'
import { ADMIN_LOGIN } from '@/lib/queries'
import { useAdminContext } from '@/context/AdminContext'
import toast from 'react-hot-toast'

export default function AdminLogin() {
  const [password, setPassword] = useState('')
  const { login } = useAdminContext()

  const [adminLogin, { loading }] = useMutation(ADMIN_LOGIN, {
    onCompleted: async (data) => {
      if (data.adminLogin.success) {
        const loginSuccess = await login(data.adminLogin.token)
        if (loginSuccess) {
          toast.success('Login successful')
        } else {
          toast.error('Token validation failed')
        }
      } else {
        toast.error(data.adminLogin.message || 'Login failed')
      }
    },
    onError: (error) => {
      toast.error('Login failed: ' + error.message)
    }
  })

  const handleSubmit = (e) => {
    e.preventDefault()
    if (!password.trim()) {
      toast.error('Please enter a password')
      return
    }
    adminLogin({ variables: { adminKey: password } })
  }

  return (
    <form onSubmit={handleSubmit}>
      <div className="mb-4">
        <label htmlFor="password" className="block text-sm font-medium text-gray-300 mb-2">
          Password
        </label>
        <input
          type="password"
          id="password"
          value={password}
          onChange={(e) => setPassword(e.target.value)}
          className="w-full px-3 py-2 bg-gray-700 border border-gray-600 rounded-lg focus:outline-none focus:border-blue-500 text-white"
          placeholder="Enter admin password"
          disabled={loading}
        />
      </div>

      <button
        type="submit"
        disabled={loading}
        className="w-full bg-blue-600 hover:bg-blue-700 disabled:bg-blue-800 text-white font-semibold py-2 px-4 rounded-lg transition-colors"
      >
        {loading ? (
          <>
            <i className="fas fa-spinner fa-spin mr-2"></i>
            Logging in...
          </>
        ) : (
          'Login'
        )}
      </button>
    </form>
  )
}
