'use client'
import { useAdminContext } from '@/context/AdminContext'
import AdminLogin from './AdminLogin'
import AdminPanel from './AdminPanel'

export default function AdminModals() {
  const { isLoginModalOpen, isAdminPanelOpen, hideLoginModal, hideAdminPanel } = useAdminContext()

  return (
    <>
      {/* Admin Login Modal */}
      {isLoginModalOpen && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <div className="bg-gray-800 rounded-lg p-6 max-w-md w-full mx-4">
            <div className="flex justify-between items-center mb-4">
              <h2 className="text-xl font-bold text-white">Admin Login</h2>
              <button
                onClick={hideLoginModal}
                className="text-gray-400 hover:text-white"
              >
                <i className="fas fa-times"></i>
              </button>
            </div>
            <AdminLogin />
          </div>
        </div>
      )}

      {/* Admin Panel Modal */}
      {isAdminPanelOpen && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <div className="bg-gray-900 rounded-lg p-6 max-w-7xl w-full h-full max-h-[90vh] mx-4 overflow-auto">
            <div className="flex justify-between items-center mb-4">
              <h2 className="text-2xl font-bold text-white">Admin Panel</h2>
              <button
                onClick={hideAdminPanel}
                className="text-gray-400 hover:text-white text-xl"
              >
                <i className="fas fa-times"></i>
              </button>
            </div>
            <AdminPanel />
          </div>
        </div>
      )}
    </>
  )
}
