'use client'

import Image from 'next/image'

export default function ContentGrid({ items = [] }) {
  if (!items || items.length === 0) {
    return (
      <div className="text-center py-12 text-gray-400">
        <p>No content available</p>
      </div>
    )
  }

  return (
    <div className="grid grid-cols-2 sm:grid-cols-3 md:grid-cols-4 lg:grid-cols-6 xl:grid-cols-8 gap-4">
      {items.map((item) => (
        <ContentCard key={item.id} item={item} />
      ))}
    </div>
  )
}

function ContentCard({ item }) {
  const handleClick = () => {
    // In free tier, just show an alert instead of navigation
    alert(`${item.title} - Video streaming is disabled in free tier`)
  }

  return (
    <div 
      className="group cursor-pointer transition-transform duration-200 hover:scale-105"
      onClick={handleClick}
    >
      <div className="relative aspect-[2/3] bg-gray-800 rounded-lg overflow-hidden">
        {item.poster ? (
          <Image
            src={item.poster}
            alt={item.title}
            fill
            className="object-cover group-hover:opacity-80 transition-opacity"
            sizes="(max-width: 640px) 50vw, (max-width: 768px) 33vw, (max-width: 1024px) 25vw, (max-width: 1280px) 16vw, 12vw"
            unoptimized
          />
        ) : (
          <div className="w-full h-full flex items-center justify-center bg-gray-700">
            <span className="text-gray-400 text-sm">No Image</span>
          </div>
        )}
        
        {/* Overlay with info */}
        <div className="absolute inset-0 bg-black bg-opacity-0 group-hover:bg-opacity-60 transition-all duration-200 flex items-end">
          <div className="p-3 text-white opacity-0 group-hover:opacity-100 transition-opacity duration-200">
            <h3 className="font-semibold text-sm line-clamp-2 mb-1">
              {item.title}
            </h3>
            <div className="text-xs text-gray-300 space-y-1">
              {item.year && (
                <p>{item.year}</p>
              )}
              {item.type && (
                <p className="uppercase text-blue-400">{item.type}</p>
              )}
              {item.rating && (
                <p>⭐ {item.rating.toFixed(1)}</p>
              )}
              {item.seasons && (
                <p>{item.seasons} Season{item.seasons !== 1 ? 's' : ''}</p>
              )}
              {item.episodes && (
                <p>{item.episodes} Episode{item.episodes !== 1 ? 's' : ''}</p>
              )}
            </div>
          </div>
        </div>

        {/* Free tier indicator */}
        <div className="absolute top-2 right-2 bg-red-600 text-white text-xs px-2 py-1 rounded opacity-0 group-hover:opacity-100 transition-opacity">
          Preview Only
        </div>
      </div>
      
      {/* Title below image */}
      <div className="mt-2 px-1">
        <h3 className="text-sm font-medium text-white line-clamp-2 leading-tight">
          {item.title}
        </h3>
        <div className="flex items-center justify-between mt-1">
          {item.year && (
            <span className="text-xs text-gray-400">{item.year}</span>
          )}
          {item.rating && (
            <span className="text-xs text-yellow-400">
              ⭐ {item.rating.toFixed(1)}
            </span>
          )}
        </div>
        {item.genre && item.genre.length > 0 && (
          <p className="text-xs text-gray-500 mt-1 line-clamp-1">
            {Array.isArray(item.genre) ? item.genre.join(', ') : item.genre}
          </p>
        )}
      </div>
    </div>
  )
}
