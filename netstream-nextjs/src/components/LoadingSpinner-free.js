'use client'

export default function LoadingSpinner({ size = 'md', text = 'Loading...' }) {
  const sizeClasses = {
    sm: 'h-4 w-4',
    md: 'h-8 w-8',
    lg: 'h-12 w-12',
    xl: 'h-16 w-16'
  }

  return (
    <div className="flex flex-col items-center justify-center py-8">
      <div className={`animate-spin border-4 border-blue-500 border-t-transparent rounded-full ${sizeClasses[size]}`}></div>
      {text && (
        <p className="mt-4 text-gray-400 text-sm">{text}</p>
      )}
    </div>
  )
}

export function InlineSpinner({ size = 'sm' }) {
  const sizeClasses = {
    sm: 'h-4 w-4',
    md: 'h-6 w-6',
    lg: 'h-8 w-8'
  }

  return (
    <div className={`animate-spin border-2 border-blue-500 border-t-transparent rounded-full ${sizeClasses[size]}`}></div>
  )
}
