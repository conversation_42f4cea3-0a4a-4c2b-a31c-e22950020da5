import { NextResponse } from 'next/server'

export async function GET(request) {
  try {
    const { searchParams } = new URL(request.url)
    const url = searchParams.get('url')
    
    if (!url) {
      return NextResponse.json({ error: 'Missing URL parameter' }, { status: 400 })
    }
    
    // Forward the request to the backend
    const backendUrl = process.env.API_URL?.replace('/graphql', '') ||
                      process.env.NEXT_PUBLIC_API_BASE_URL ||
                      'http://localhost:3001'
    const proxyUrl = `${backendUrl}/proxy-token?url=${encodeURIComponent(url)}`
    
    console.log('Forwarding proxy token request to:', proxyUrl)
    
    const response = await fetch(proxyUrl, {
      method: 'GET',
      headers: {
        'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36'
      }
    })
    
    if (!response.ok) {
      console.error('Backend proxy token request failed:', response.status, response.statusText)
      return NextResponse.json(
        { error: `Backend request failed: ${response.status} ${response.statusText}` }, 
        { status: response.status }
      )
    }
    
    const proxyTokenUrl = await response.text()
    console.log('Got proxy token URL from backend:', proxyTokenUrl)
    
    // Return the proxy URL as plain text
    return new NextResponse(proxyTokenUrl, {
      status: 200,
      headers: {
        'Content-Type': 'text/plain',
        'Cache-Control': 'no-cache, no-store, must-revalidate'
      }
    })
    
  } catch (error) {
    console.error('Proxy token API error:', error)
    return NextResponse.json(
      { error: `Proxy token error: ${error.message}` }, 
      { status: 500 }
    )
  }
}
