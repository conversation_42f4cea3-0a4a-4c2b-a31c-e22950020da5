import { NextResponse } from 'next/server';
import { connectToDatabase } from '../../../../lib/mongodb';

// Extract the base URL from API_URL (server-side) or NEXT_PUBLIC_API_URL (client-side) or use default
const BACKEND_URL = (process.env.API_URL || process.env.NEXT_PUBLIC_API_URL || 'http://netstream-backend-container:3001/graphql').replace('/graphql', '');

export async function POST() {
  try {
    const db = await connectToDatabase();
    
    // Find the currently running scraping job
    const runningJob = await db.collection('scraping_jobs')
      .findOne({ status: 'running' }, { sort: { startedAt: -1 } });

    if (!runningJob) {
      return NextResponse.json({
        success: false,
        error: 'No running scraping job found'
      }, { status: 404 });
    }

    console.log(`Stopping scraping job: ${runningJob.jobId}`);

    // Call the backend stop endpoint first
    try {
      const backendResponse = await fetch(`${BACKEND_URL}/api/scrape/stop`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        }
      });

      const backendResult = await backendResponse.json();

      if (backendResult.success) {
        console.log('Backend stop signal sent successfully');
      } else {
        console.warn('Backend stop signal failed:', backendResult.error);
      }
    } catch (backendError) {
      console.warn('Could not send stop signal to backend:', backendError.message);
      // Continue with database update even if backend call fails
    }

    // Update job status to stopped
    await db.collection('scraping_jobs').updateOne(
      { jobId: runningJob.jobId },
      {
        $set: {
          status: 'stopped',
          stoppedAt: new Date(),
          stoppedBy: 'admin'
        }
      }
    );

    const stopData = {
      jobId: runningJob.jobId,
      status: 'stopped',
      stoppedAt: new Date().toISOString(),
      message: 'Scraping job stopped by admin'
    };

    console.log(`Scraping job ${runningJob.jobId} stopped successfully`);

    return NextResponse.json({
      success: true,
      message: 'Scraping job stopped successfully',
      data: stopData
    });

  } catch (error) {
    console.error('Stop scraping error:', error);
    return NextResponse.json({
      success: false,
      error: `Failed to stop scraping: ${error.message}`
    }, { status: 500 });
  }
} 