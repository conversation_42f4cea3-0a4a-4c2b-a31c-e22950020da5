import { NextResponse } from 'next/server'

export async function GET(request) {
  try {
    const { searchParams } = new URL(request.url)
    const url = searchParams.get('url')
    
    if (!url) {
      return NextResponse.json({ error: 'Missing URL parameter' }, { status: 400 })
    }
    
    console.log('Stream proxy request for:', url)
    
    // Forward the request to the backend stream proxy
    const backendUrl = process.env.API_URL?.replace('/graphql', '') || 
                      process.env.NEXT_PUBLIC_API_BASE_URL || 
                      'http://localhost:3001'
    const proxyUrl = `${backendUrl}/stream-proxy?url=${encodeURIComponent(url)}`
    
    console.log('Forwarding to backend stream proxy:', proxyUrl)
    
    const response = await fetch(proxyUrl, {
      method: 'GET',
      headers: {
        'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36',
        'Accept': '*/*',
        'Accept-Language': 'en-US,en;q=0.9',
        'Cache-Control': 'no-cache',
        'Pragma': 'no-cache'
      }
    })
    
    if (!response.ok) {
      console.error('Backend stream proxy request failed:', response.status, response.statusText)
      return NextResponse.json(
        { error: `Backend stream proxy failed: ${response.status} ${response.statusText}` }, 
        { status: response.status }
      )
    }
    
    // Get the content type from the backend response
    const contentType = response.headers.get('content-type') || 'application/vnd.apple.mpegurl'
    
    // Stream the response back to the client
    const responseBody = await response.arrayBuffer()
    
    console.log('Stream proxy successful, content-type:', contentType, 'size:', responseBody.byteLength)
    
    return new NextResponse(responseBody, {
      status: 200,
      headers: {
        'Content-Type': contentType,
        'Access-Control-Allow-Origin': '*',
        'Access-Control-Allow-Methods': 'GET, HEAD, OPTIONS',
        'Access-Control-Allow-Headers': 'Range, Content-Range, Content-Length',
        'Cache-Control': 'no-cache, no-store, must-revalidate',
        'Pragma': 'no-cache',
        'Expires': '0'
      }
    })
    
  } catch (error) {
    console.error('Stream proxy API error:', error)
    return NextResponse.json(
      { error: `Stream proxy error: ${error.message}` }, 
      { status: 500 }
    )
  }
}

// Handle OPTIONS requests for CORS preflight
export async function OPTIONS(request) {
  return new NextResponse(null, {
    status: 200,
    headers: {
      'Access-Control-Allow-Origin': '*',
      'Access-Control-Allow-Methods': 'GET, HEAD, OPTIONS',
      'Access-Control-Allow-Headers': 'Range, Content-Range, Content-Length',
    }
  })
}
