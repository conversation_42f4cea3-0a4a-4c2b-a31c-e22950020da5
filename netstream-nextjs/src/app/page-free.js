'use client'

import { useQuery } from '@apollo/client'
import { useState } from 'react'
import { GET_MOVIES, GET_SERIES, GET_ANIME, SEARCH_CONTENT } from '../lib/queries-free'
import ContentGrid from '../components/ContentGrid-free'
import SearchBar from '../components/SearchBar-free'
import LoadingSpinner from '../components/LoadingSpinner-free'

export default function HomePage() {
  const [searchQuery, setSearchQuery] = useState('')
  const [searchResults, setSearchResults] = useState([])
  const [isSearching, setIsSearching] = useState(false)

  // Fetch content with reduced limits for free tier
  const { data: moviesData, loading: moviesLoading } = useQuery(GET_MOVIES, {
    variables: { page: 1, limit: 8 }
  })
  
  const { data: seriesData, loading: seriesLoading } = useQuery(GET_SERIES, {
    variables: { page: 1, limit: 8 }
  })
  
  const { data: animeData, loading: animeLoading } = useQuery(GET_ANIME, {
    variables: { page: 1, limit: 8 }
  })

  // Search functionality
  const handleSearch = async (query) => {
    if (!query.trim()) {
      setSearchResults([])
      setSearchQuery('')
      return
    }

    setIsSearching(true)
    setSearchQuery(query)

    try {
      // Note: In a real implementation, you'd use Apollo's useLazyQuery
      // For simplicity in free tier, we'll simulate the search
      const response = await fetch('/graphql', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          query: `
            query SearchContent($query: String!, $limit: Int) {
              search(query: $query, limit: $limit) {
                id
                title
                type
                poster
                year
              }
            }
          `,
          variables: { query, limit: 5 }
        })
      })

      const result = await response.json()
      setSearchResults(result.data?.search || [])
    } catch (error) {
      console.error('Search error:', error)
      setSearchResults([])
    } finally {
      setIsSearching(false)
    }
  }

  const clearSearch = () => {
    setSearchQuery('')
    setSearchResults([])
  }

  return (
    <div className="space-y-8">
      {/* Hero Section */}
      <div className="text-center py-12 bg-gradient-to-r from-blue-900 to-purple-900 rounded-lg">
        <h1 className="text-4xl font-bold mb-4">
          Welcome to NetStream
        </h1>
        <p className="text-xl text-gray-300 mb-6">
          Free Tier Demo - Browse our content library
        </p>
        
        {/* Search Bar */}
        <div className="max-w-md mx-auto">
          <SearchBar 
            onSearch={handleSearch}
            onClear={clearSearch}
            placeholder="Search movies, series, anime..."
            isLoading={isSearching}
          />
        </div>
      </div>

      {/* Search Results */}
      {searchQuery && (
        <div>
          <div className="flex items-center justify-between mb-4">
            <h2 className="text-2xl font-bold">
              Search Results for "{searchQuery}"
            </h2>
            <button
              onClick={clearSearch}
              className="text-gray-400 hover:text-white text-sm"
            >
              Clear Search
            </button>
          </div>
          
          {isSearching ? (
            <LoadingSpinner />
          ) : searchResults.length > 0 ? (
            <ContentGrid items={searchResults} />
          ) : (
            <div className="text-center py-8 text-gray-400">
              No results found for "{searchQuery}"
            </div>
          )}
        </div>
      )}

      {/* Content Sections - Only show when not searching */}
      {!searchQuery && (
        <>
          {/* Movies Section */}
          <div>
            <div className="flex items-center justify-between mb-6">
              <h2 className="text-2xl font-bold">Latest Movies</h2>
              <a 
                href="/movies" 
                className="text-blue-400 hover:text-blue-300 text-sm font-medium"
              >
                View All →
              </a>
            </div>
            
            {moviesLoading ? (
              <LoadingSpinner />
            ) : (
              <ContentGrid items={moviesData?.movies || []} />
            )}
          </div>

          {/* Series Section */}
          <div>
            <div className="flex items-center justify-between mb-6">
              <h2 className="text-2xl font-bold">Latest Series</h2>
              <a 
                href="/series" 
                className="text-blue-400 hover:text-blue-300 text-sm font-medium"
              >
                View All →
              </a>
            </div>
            
            {seriesLoading ? (
              <LoadingSpinner />
            ) : (
              <ContentGrid items={seriesData?.series || []} />
            )}
          </div>

          {/* Anime Section */}
          <div>
            <div className="flex items-center justify-between mb-6">
              <h2 className="text-2xl font-bold">Latest Anime</h2>
              <a 
                href="/anime" 
                className="text-blue-400 hover:text-blue-300 text-sm font-medium"
              >
                View All →
              </a>
            </div>
            
            {animeLoading ? (
              <LoadingSpinner />
            ) : (
              <ContentGrid items={animeData?.anime || []} />
            )}
          </div>
        </>
      )}

      {/* Free Tier Limitations Notice */}
      <div className="bg-yellow-900 border border-yellow-700 rounded-lg p-6 mt-12">
        <h3 className="text-lg font-semibold text-yellow-200 mb-2">
          Free Tier Limitations
        </h3>
        <ul className="text-yellow-300 text-sm space-y-1">
          <li>• Limited to 10 items per page</li>
          <li>• Search results limited to 5 items</li>
          <li>• Video streaming disabled</li>
          <li>• Admin panel disabled</li>
          <li>• Content scraping disabled</li>
          <li>• Service may sleep after 15 minutes of inactivity</li>
        </ul>
        <p className="text-yellow-200 text-sm mt-3">
          This is a demonstration of the NetStream platform running on Render.com's free tier.
        </p>
      </div>
    </div>
  )
}
