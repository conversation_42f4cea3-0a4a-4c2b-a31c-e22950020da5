'use client'

import { Inter } from 'next/font/google'
import './globals.css'
import { ApolloProvider } from '@apollo/client'
import { createApolloClient } from '../lib/apollo-free'
import { Toaster } from 'react-hot-toast'

const inter = Inter({ subsets: ['latin'] })

// Free tier Apollo client (simplified)
const client = createApolloClient()

export default function RootLayout({ children }) {
  return (
    <html lang="en">
      <head>
        <title>NetStream - Free Tier</title>
        <meta name="description" content="NetStream streaming platform - Free tier demo" />
        <meta name="viewport" content="width=device-width, initial-scale=1" />
        <link rel="icon" href="/favicon.ico" />
      </head>
      <body className={inter.className}>
        <ApolloProvider client={client}>
          <div className="min-h-screen bg-gray-900 text-white">
            {/* Free Tier Header */}
            <header className="bg-gray-800 border-b border-gray-700">
              <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
                <div className="flex justify-between items-center h-16">
                  <div className="flex items-center">
                    <h1 className="text-xl font-bold text-white">
                      NetStream
                    </h1>
                    <span className="ml-2 px-2 py-1 text-xs bg-blue-600 text-white rounded">
                      FREE TIER
                    </span>
                  </div>
                  
                  {/* Simple Navigation */}
                  <nav className="hidden md:flex space-x-8">
                    <a href="/" className="text-gray-300 hover:text-white px-3 py-2 text-sm font-medium">
                      Home
                    </a>
                    <a href="/movies" className="text-gray-300 hover:text-white px-3 py-2 text-sm font-medium">
                      Movies
                    </a>
                    <a href="/series" className="text-gray-300 hover:text-white px-3 py-2 text-sm font-medium">
                      Series
                    </a>
                    <a href="/anime" className="text-gray-300 hover:text-white px-3 py-2 text-sm font-medium">
                      Anime
                    </a>
                  </nav>
                </div>
              </div>
            </header>

            {/* Free Tier Notice */}
            <div className="bg-blue-900 border-b border-blue-800">
              <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-2">
                <p className="text-center text-sm text-blue-200">
                  🎬 This is a free tier demo with limited features. 
                  <span className="font-medium"> Video streaming and admin features are disabled.</span>
                </p>
              </div>
            </div>

            {/* Main Content */}
            <main className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
              {children}
            </main>

            {/* Free Tier Footer */}
            <footer className="bg-gray-800 border-t border-gray-700 mt-16">
              <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
                <div className="text-center">
                  <p className="text-gray-400 text-sm">
                    NetStream Free Tier Demo - Limited functionality for demonstration purposes
                  </p>
                  <p className="text-gray-500 text-xs mt-2">
                    Powered by Render.com Free Tier
                  </p>
                </div>
              </div>
            </footer>
          </div>
          
          {/* Toast notifications */}
          <Toaster
            position="top-right"
            toastOptions={{
              duration: 4000,
              style: {
                background: '#374151',
                color: '#fff',
              },
            }}
          />
        </ApolloProvider>
      </body>
    </html>
  )
}
