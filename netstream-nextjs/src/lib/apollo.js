'use client'
import { <PERSON>Client, InMemoryCache, createHttpLink } from '@apollo/client'
import { setContext } from '@apollo/client/link/context'

// Enhanced API URL resolution for Render.com deployment
const getApiUrl = () => {
  // During build time, use a placeholder URL to prevent connection errors
  if (process.env.SKIP_BUILD_STATIC_GENERATION === 'true' || typeof window === 'undefined') {
    return 'http://localhost:3001/graphql';
  }

  // Priority order for API URL resolution
  const apiUrl = process.env.NEXT_PUBLIC_API_URL ||
                 process.env.NEXT_PUBLIC_API_BASE_URL ||
                 process.env.NEXT_PUBLIC_BACKEND_URL ||
                 'http://localhost:3001';

  // Ensure URL ends with /graphql
  return apiUrl.endsWith('/graphql') ? apiUrl : `${apiUrl}/graphql`;
};

const httpLink = createHttpLink({
  uri: getApiUrl(),
  credentials: 'include', // Important for CORS with credentials
  headers: {
    'Content-Type': 'application/json',
  },
  // Add error handling for build time
  fetch: (uri, options) => {
    if (process.env.SKIP_BUILD_STATIC_GENERATION === 'true') {
      // Return a mock response during build time
      return Promise.resolve({
        ok: true,
        status: 200,
        json: () => Promise.resolve({ data: {} }),
        text: () => Promise.resolve('{}'),
      });
    }
    return fetch(uri, options);
  }
})

const authLink = setContext((_, { headers }) => {
  // Get admin token if available
  const token = typeof window !== 'undefined' ? localStorage.getItem('adminToken') : null

  return {
    headers: {
      ...headers,
      authorization: token ? `Bearer ${token}` : '',
    }
  }
})

const client = new ApolloClient({
  link: authLink.concat(httpLink),
  cache: new InMemoryCache({
    typePolicies: {
      Query: {
        fields: {
          movies: {
            merge(existing = [], incoming) {
              return [...existing, ...incoming]
            }
          },
          series: {
            merge(existing = [], incoming) {
              return [...existing, ...incoming]
            }
          },
          anime: {
            merge(existing = [], incoming) {
              return [...existing, ...incoming]
            }
          },
          liveTV: {
            merge(existing = [], incoming) {
              return [...existing, ...incoming]
            }
          }
        }
      }
    }
  }),
  defaultOptions: {
    watchQuery: {
      errorPolicy: 'all',
      notifyOnNetworkStatusChange: true
    },
    query: {
      errorPolicy: 'all',
      notifyOnNetworkStatusChange: true
    }
  },
  // Add connection error handling for build time
  connectToDevTools: false,
  ssrMode: typeof window === 'undefined'
})

export { client }

export function ApolloWrapper({ children }) {
  const { ApolloProvider } = require('@apollo/client')
  
  return (
    <ApolloProvider client={client}>
      {children}
    </ApolloProvider>
  )
}
