import { MongoClient } from 'mongodb';

const uri = process.env.MONGO_URI;
let client;
let clientPromise;

// Handle build-time gracefully
if (!process.env.MONGO_URI) {
  if (process.env.SKIP_BUILD_STATIC_GENERATION === 'true') {
    console.warn('MongoDB URI not provided during build - using dummy connection');
  } else {
    throw new Error('Please add your MONGO_URI to .env');
  }
}

// Handle build-time execution
if (process.env.SKIP_BUILD_STATIC_GENERATION === 'true') {
  // During build, return a mock client
  clientPromise = Promise.resolve({
    db: () => ({
      collection: () => ({
        insertOne: () => Promise.resolve({ insertedId: 'mock' }),
        find: () => ({ toArray: () => Promise.resolve([]) }),
        findOne: () => Promise.resolve(null),
        updateOne: () => Promise.resolve({ modifiedCount: 0 }),
        deleteOne: () => Promise.resolve({ deletedCount: 0 })
      })
    })
  });
} else if (process.env.NODE_ENV === 'development') {
  // In development mode, use a global variable so that the value
  // is preserved across module reloads caused by HMR (Hot Module Replacement).
  if (!global._mongoClientPromise) {
    client = new MongoClient(uri);
    global._mongoClientPromise = client.connect();
  }
  clientPromise = global._mongoClientPromise;
} else {
  // In production mode, it's best to not use a global variable.
  client = new MongoClient(uri);
  clientPromise = client.connect();
}

export async function connectToDatabase() {
  if (process.env.SKIP_BUILD_STATIC_GENERATION === 'true') {
    // Return mock database during build
    return {
      collection: () => ({
        insertOne: () => Promise.resolve({ insertedId: 'mock' }),
        find: () => ({ toArray: () => Promise.resolve([]) }),
        findOne: () => Promise.resolve(null),
        updateOne: () => Promise.resolve({ modifiedCount: 0 }),
        deleteOne: () => Promise.resolve({ deletedCount: 0 })
      })
    };
  }

  const client = await clientPromise;
  return client.db();
}

// Export a module-scoped MongoClient promise. By doing this in a
// separate module, the client can be shared across functions.
export default clientPromise; 