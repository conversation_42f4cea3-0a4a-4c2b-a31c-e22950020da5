import { gql } from '@apollo/client'

// Free tier GraphQL queries - simplified and optimized

export const GET_MOVIES = gql`
  query GetMovies($page: Int, $limit: Int) {
    movies(page: $page, limit: $limit) {
      id
      title
      year
      poster
      genre
      rating
    }
  }
`

export const GET_SERIES = gql`
  query GetSeries($page: Int, $limit: Int) {
    series(page: $page, limit: $limit) {
      id
      title
      year
      poster
      genre
      seasons
    }
  }
`

export const GET_ANIME = gql`
  query GetAnime($page: Int, $limit: Int) {
    anime(page: $page, limit: $limit) {
      id
      title
      year
      poster
      genre
      episodes
    }
  }
`

export const SEARCH_CONTENT = gql`
  query SearchContent($query: String!, $limit: Int) {
    search(query: $query, limit: $limit) {
      id
      title
      type
      poster
      year
    }
  }
`

export const GET_HEALTH = gql`
  query GetHealth {
    health {
      status
      memory {
        rss
        heapTotal
        heapUsed
      }
      cache {
        size
        hits
        misses
        hitRate
      }
      uptime
    }
  }
`
