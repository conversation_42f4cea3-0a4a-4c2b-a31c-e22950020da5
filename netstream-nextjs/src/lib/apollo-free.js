import { ApolloClient, InMemoryCache, createHttpLink } from '@apollo/client'

// Free tier GraphQL endpoint
const httpLink = createHttpLink({
  uri: process.env.NEXT_PUBLIC_API_URL || '/graphql',
  credentials: 'same-origin'
})

// Simplified cache configuration for free tier
const cache = new InMemoryCache({
  typePolicies: {
    Query: {
      fields: {
        movies: {
          keyArgs: ['page', 'limit'],
          merge(existing = [], incoming) {
            return incoming // Simple replacement for free tier
          }
        },
        series: {
          keyArgs: ['page', 'limit'],
          merge(existing = [], incoming) {
            return incoming
          }
        },
        anime: {
          keyArgs: ['page', 'limit'],
          merge(existing = [], incoming) {
            return incoming
          }
        },
        search: {
          keyArgs: ['query', 'limit'],
          merge(existing = [], incoming) {
            return incoming
          }
        }
      }
    }
  }
})

export function createApolloClient() {
  return new ApolloClient({
    link: httpLink,
    cache,
    defaultOptions: {
      watchQuery: {
        errorPolicy: 'all',
        fetchPolicy: 'cache-first' // Aggressive caching for free tier
      },
      query: {
        errorPolicy: 'all',
        fetchPolicy: 'cache-first'
      }
    },
    connectToDevTools: process.env.NODE_ENV === 'development'
  })
}

export default createApolloClient
