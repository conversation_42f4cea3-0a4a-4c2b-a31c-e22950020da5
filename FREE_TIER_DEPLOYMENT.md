# NetStream Free Tier Deployment Guide

## Overview

This guide will help you deploy NetStream to Render.com's **FREE TIER** with optimized configuration for the 512MB RAM and 0.1 vCPU limitations.

## What's Included in Free Tier

### ✅ Available Features
- **Content Browsing**: Movies, series, and anime metadata
- **Search Functionality**: Basic search across all content types
- **Responsive UI**: Mobile and desktop optimized interface
- **GraphQL API**: Optimized queries with caching
- **Health Monitoring**: System health and performance metrics

### ❌ Disabled Features (to fit free tier)
- **Video Streaming**: Disabled to reduce memory usage
- **Content Scraping**: Disabled (uses existing database content)
- **Admin Panel**: Disabled to reduce complexity
- **Background Jobs**: Disabled (no Bull queues)
- **AI Enrichment**: Disabled (no Gemini API calls)
- **Redis**: Replaced with in-memory cache

## Pre-Deployment Setup

### 1. Environment Variables Required

You'll need to set these in Render.com dashboard:

```bash
# Essential (Required)
MONGO_URI=mongodb+srv://your-connection-string
TMDB_API_KEY=your-tmdb-api-key

# Optional (for enhanced metadata)
# Leave empty if you don't have them
JIKAN_API_KEY=
GEMINI_API_KEY=
```

### 2. MongoDB Atlas Setup

Ensure your MongoDB Atlas cluster:
- Allows connections from `0.0.0.0/0` (Render's dynamic IPs)
- Has the NetStream database with content collections
- Connection string includes `retryWrites=true&w=majority`

## Deployment Steps

### Step 1: Fork/Clone Repository

1. Ensure you're on the `render-deployment` branch
2. Verify all free tier files are present:
   - `Dockerfile.free`
   - `server-free.js`
   - `package-free.json`
   - `render.yaml`

### Step 2: Create Render.com Account

1. Go to [render.com](https://render.com)
2. Sign up with GitHub account
3. Connect your NetStream repository

### Step 3: Deploy Service

#### Option A: Using render.yaml (Recommended)

1. In Render dashboard, click "New +"
2. Select "Blueprint"
3. Connect your repository
4. Select `render-deployment` branch
5. Render will automatically read `render.yaml`
6. Set required environment variables:
   - `MONGO_URI`: Your MongoDB connection string
   - `TMDB_API_KEY`: Your TMDB API key

#### Option B: Manual Setup

1. In Render dashboard, click "New +"
2. Select "Web Service"
3. Connect your repository
4. Configure:
   - **Name**: `netstream-free-tier`
   - **Branch**: `render-deployment`
   - **Environment**: Docker
   - **Dockerfile Path**: `./Dockerfile.free`
   - **Build Command**: (leave empty)
   - **Start Command**: `node server-free.js`

### Step 4: Configure Environment Variables

In the Render dashboard, add these environment variables:

```bash
# Database
MONGO_URI=mongodb+srv://crypto:<EMAIL>/NetStream?retryWrites=true&w=majority

# API Keys
TMDB_API_KEY=94b3e867d01d17b6de1f13d5775bf60a

# Free Tier Flags (auto-set by render.yaml)
RENDER_FREE_TIER=true
NODE_ENV=production
```

### Step 5: Deploy and Monitor

1. Click "Create Web Service"
2. Wait for build to complete (5-10 minutes)
3. Monitor logs for any errors
4. Test health endpoint: `https://your-app.onrender.com/health`

## Post-Deployment Verification

### 1. Health Check

Visit: `https://your-app.onrender.com/health`

Expected response:
```json
{
  "status": "ok",
  "timestamp": "2024-01-01T00:00:00.000Z",
  "uptime": 123.45,
  "memory": {
    "rss": "150MB",
    "heapTotal": "120MB", 
    "heapUsed": "80MB"
  },
  "cache": {
    "size": 45,
    "hits": 123,
    "misses": 67,
    "hitRate": 65
  },
  "database": "connected"
}
```

### 2. GraphQL Playground

Visit: `https://your-app.onrender.com/graphql`

Test query:
```graphql
query {
  movies(page: 1, limit: 5) {
    id
    title
    year
    poster
  }
}
```

### 3. Frontend Interface

Visit: `https://your-app.onrender.com/`

Should show:
- NetStream homepage with "FREE TIER" badge
- Content grids for movies, series, anime
- Search functionality
- Free tier limitations notice

## Performance Expectations

### Memory Usage
- **Target**: <400MB RAM usage
- **Typical**: 150-250MB under normal load
- **Peak**: 300-350MB during high traffic

### Response Times
- **Cold Start**: 30-45 seconds (free tier limitation)
- **Warm Responses**: 200-500ms
- **GraphQL Queries**: 100-300ms
- **Search**: 300-800ms

### Limitations
- **Sleep Time**: Service sleeps after 15 minutes of inactivity
- **Concurrent Users**: 10-20 maximum
- **Build Time**: 15 minutes maximum
- **Bandwidth**: 100GB/month

## Troubleshooting

### Common Issues

#### 1. Build Fails
```bash
# Check Dockerfile.free exists
ls -la Dockerfile.free

# Verify package-free.json dependencies
cat package-free.json
```

#### 2. Memory Errors
```bash
# Check memory usage in logs
# Look for "JavaScript heap out of memory"

# Solution: Reduce cache sizes in server-free.js
MAX_CACHE_SIZE=100  # Reduce from 200
```

#### 3. Database Connection Issues
```bash
# Verify MongoDB URI format
MONGO_URI=mongodb+srv://user:<EMAIL>/NetStream?retryWrites=true&w=majority

# Check IP whitelist in MongoDB Atlas
# Add 0.0.0.0/0 for Render
```

#### 4. Service Won't Start
```bash
# Check logs in Render dashboard
# Common issues:
# - Missing environment variables
# - Invalid MongoDB connection
# - Port binding issues
```

### Performance Optimization

#### 1. Reduce Memory Usage
```javascript
// In server-free.js, reduce cache size
const cache = new FreeTierCache();
cache.maxSize = 100; // Reduce from 200
```

#### 2. Optimize Database Queries
```javascript
// Limit query results
.limit(5) // Instead of 10
```

#### 3. Enable Compression
```javascript
// Already enabled in server-free.js
await fastify.register(require('@fastify/compress'));
```

## Monitoring

### 1. Render Dashboard
- Monitor service status
- Check memory and CPU usage
- Review deployment logs
- Set up alerts for downtime

### 2. Health Endpoint
- Monitor `/health` endpoint
- Track memory usage trends
- Monitor cache hit rates
- Check database connectivity

### 3. Application Logs
```bash
# Key metrics to monitor
- Memory usage: <400MB
- Response times: <1000ms
- Error rates: <5%
- Cache hit rate: >60%
```

## Scaling Considerations

### When to Upgrade from Free Tier

Consider upgrading to Starter plan ($7/month) when:
- Memory usage consistently >350MB
- Response times >2 seconds
- Frequent service sleeping affects users
- Need for disabled features (video streaming, admin panel)

### Upgrade Path

1. **Starter Plan**: $7/month, 512MB RAM, 0.5 vCPU
2. **Standard Plan**: $25/month, 2GB RAM, 1 vCPU
3. **Pro Plan**: $85/month, 4GB RAM, 2 vCPU

## Success Metrics

### Technical KPIs
- **Uptime**: >95% (accounting for free tier sleep)
- **Response Time**: <1000ms average
- **Memory Usage**: <400MB peak
- **Error Rate**: <5%

### User Experience
- **Page Load**: <3 seconds
- **Search Response**: <1 second
- **Content Display**: Immediate from cache
- **Mobile Responsive**: Full functionality

## Next Steps

After successful free tier deployment:

1. **Test thoroughly** with real users
2. **Monitor performance** for 1-2 weeks
3. **Collect user feedback** on limitations
4. **Plan upgrade** to paid tier for full features
5. **Consider Android TV app** development

This free tier deployment serves as an excellent proof-of-concept and demo platform for the full NetStream application.
