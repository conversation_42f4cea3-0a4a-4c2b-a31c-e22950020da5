{"name": "netstream-free-tier", "version": "1.0.0", "description": "NetStream - Free Tier Streaming Platform", "main": "server-free.js", "scripts": {"start": "node server-free.js", "dev": "nodemon server-free.js", "health": "curl http://localhost:3000/health", "test": "echo \"No tests for free tier\" && exit 0"}, "keywords": ["streaming", "graphql", "fastify", "free-tier"], "author": "NetStream Team", "license": "ISC", "engines": {"node": ">=18.0.0"}, "dependencies": {"@fastify/cors": "^9.0.1", "@fastify/static": "^7.0.4", "axios": "^1.8.4", "dotenv": "^16.5.0", "fastify": "^4.26.2", "graphql": "^16.8.1", "mercurius": "^13.3.3", "mongodb": "^6.3.0", "pino": "^8.19.0", "pino-pretty": "^11.0.0"}, "devDependencies": {"nodemon": "^3.0.3"}}