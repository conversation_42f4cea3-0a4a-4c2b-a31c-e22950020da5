#!/usr/bin/env node

/**
 * Debug script to check database contents and find the Gemini API key
 */

const { MongoClient } = require('mongodb');
require('dotenv').config();

async function debugDatabase() {
  try {
    console.log('🔍 Connecting to MongoDB...');
    const uri = process.env.MONGO_URI;
    console.log('📍 MongoDB URI:', uri ? 'Found' : 'Not found');
    
    if (!uri) {
      throw new Error('MONGO_URI not found in environment variables');
    }
    
    const client = new MongoClient(uri);
    await client.connect();
    console.log('✅ Connected to MongoDB successfully');
    
    // List all databases
    console.log('\n=== Available Databases ===');
    const adminDb = client.db().admin();
    const databases = await adminDb.listDatabases();
    databases.databases.forEach(db => {
      console.log(`📁 Database: ${db.name}`);
    });
    
    // Check different possible database names
    const possibleDbNames = ['netstream', 'NetStream', 'netstream_db'];
    
    for (const dbName of possibleDbNames) {
      console.log(`\n=== Checking Database: ${dbName} ===`);
      const db = client.db(dbName);
      
      try {
        // List collections
        const collections = await db.listCollections().toArray();
        console.log(`📂 Collections in ${dbName}:`);
        collections.forEach(col => {
          console.log(`  - ${col.name}`);
        });
        
        // Check config collection if it exists
        const configExists = collections.some(col => col.name === 'config');
        if (configExists) {
          console.log(`\n🔧 Checking config collection in ${dbName}:`);
          const configDocs = await db.collection('config').find({}).toArray();
          console.log(`📄 Found ${configDocs.length} documents in config collection:`);
          
          configDocs.forEach((doc, index) => {
            console.log(`  ${index + 1}. Key: "${doc.key}", Value: ${doc.value ? '***HIDDEN***' : 'null'}`);
            if (doc.key === 'GEMINI_API_KEY') {
              console.log(`     🎯 FOUND GEMINI_API_KEY! Value length: ${doc.value ? doc.value.length : 0}`);
            }
          });
          
          // Specifically search for GEMINI_API_KEY
          const geminiConfig = await db.collection('config').findOne({ key: 'GEMINI_API_KEY' });
          if (geminiConfig) {
            console.log(`\n✅ GEMINI_API_KEY found in ${dbName}!`);
            console.log(`   Key: ${geminiConfig.key}`);
            console.log(`   Value: ${geminiConfig.value ? geminiConfig.value.substring(0, 10) + '...' : 'null'}`);
            console.log(`   Last Updated: ${geminiConfig.lastUpdated || 'Not set'}`);
          } else {
            console.log(`\n❌ GEMINI_API_KEY not found in ${dbName} config collection`);
          }
        } else {
          console.log(`❌ No config collection found in ${dbName}`);
        }
        
      } catch (error) {
        console.log(`❌ Error accessing ${dbName}: ${error.message}`);
      }
    }
    
    await client.close();
    console.log('\n🔐 Database connection closed');
    
  } catch (error) {
    console.error('💥 Error:', error.message);
  }
}

// Run the debug
debugDatabase().catch(error => {
  console.error('💥 Unexpected error:', error);
  process.exit(1);
});
