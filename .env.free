# NetStream Free Tier Environment Configuration
# Optimized for Render.com Free Tier (512MB RAM, 0.1 vCPU)

# Application Environment
NODE_ENV=production
PORT=3000

# Database Configuration
MONGO_URI=mongodb+srv://crypto:<EMAIL>/NetStream?retryWrites=true&w=majority

# Free Tier Feature Flags
RENDER_FREE_TIER=true
ENABLE_SCRAPING=false
ENABLE_BACKGROUND_JOBS=false
ENABLE_ADVANCED_ENRICHMENT=false
ENABLE_PUPPETEER=false
ENABLE_REDIS=false
ENABLE_VIDEO_STREAMING=false
ENABLE_ADMIN_PANEL=false

# API Keys (Optional for free tier)
TMDB_API_KEY=94b3e867d01d17b6de1f13d5775bf60a

# Performance Limits for Free Tier
MAX_CONCURRENT_REQUESTS=5
MAX_ITEMS_PER_PAGE=10
MAX_SEARCH_RESULTS=5
MAX_CACHE_SIZE=200
CACHE_TTL=1800000

# Memory Optimization
NODE_OPTIONS=--max-old-space-size=400

# Logging Configuration
LOG_LEVEL=warn
DISABLE_REQUEST_LOGGING=true

# Database Connection Limits
DB_MAX_POOL_SIZE=5
DB_MIN_POOL_SIZE=1
DB_CONNECTION_TIMEOUT=5000

# Cache Strategy
CACHE_STRATEGY=minimal
ENABLE_QUERY_CACHE=true
ENABLE_RESULT_CACHE=true

# Security (Basic)
CORS_ORIGIN=*
TRUST_PROXY=true

# Health Check
HEALTH_CHECK_INTERVAL=30000

# Render.com Specific
RENDER=true
