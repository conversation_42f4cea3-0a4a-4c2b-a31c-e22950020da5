# Android TV App Development Plan for NetStream

## Project Overview

### Objective
Develop a native Android TV application for the NetStream streaming platform that provides an optimized 10-foot UI experience with full remote control navigation and high-performance video streaming.

### Technology Stack Decision

#### Recommended Approach: React Native TV
**Rationale**:
- 60-70% code reuse from existing React web components
- Faster development timeline (2-3 months vs 4-6 months native)
- Shared business logic and API integration
- Easier maintenance with unified codebase
- Strong community support for TV development

#### Alternative Approaches Considered
1. **Native Android TV (Kotlin)**: Best performance but requires complete rewrite
2. **Progressive Web App**: Fastest deployment but limited TV integration
3. **Flutter TV**: Good performance but less mature TV ecosystem

## Technical Architecture

### Project Structure
```
NetStreamTV/
├── src/
│   ├── components/          # Reusable UI components
│   │   ├── common/         # Shared components
│   │   ├── tv/             # TV-specific components
│   │   └── media/          # Media-related components
│   ├── screens/            # Application screens
│   │   ├── Home/           # Home screen with carousels
│   │   ├── Browse/         # Content browsing
│   │   ├── Player/         # Video player screen
│   │   ├── Search/         # Search functionality
│   │   └── Settings/       # App settings
│   ├── navigation/         # Navigation configuration
│   ├── services/           # API and business logic
│   │   ├── api/            # GraphQL client
│   │   ├── auth/           # Authentication
│   │   └── storage/        # Local storage
│   ├── utils/              # Utility functions
│   ├── hooks/              # Custom React hooks
│   └── constants/          # App constants
├── android/                # Android-specific code
├── assets/                 # Images, fonts, etc.
└── __tests__/              # Test files
```

### Core Dependencies
```json
{
  "dependencies": {
    "react": "18.2.0",
    "react-native": "0.72.0",
    "@react-native-community/tv": "0.72.0",
    "react-native-video": "5.2.1",
    "@apollo/client": "3.8.0",
    "react-navigation": "6.0.0",
    "@react-navigation/native": "6.1.0",
    "@react-navigation/stack": "6.3.0",
    "react-native-vector-icons": "10.0.0",
    "react-native-linear-gradient": "2.8.0",
    "react-native-fast-image": "8.6.0",
    "react-native-keychain": "8.1.0",
    "react-native-device-info": "10.8.0",
    "react-native-orientation-locker": "1.5.0"
  },
  "devDependencies": {
    "@types/react": "18.2.0",
    "@types/react-native": "0.72.0",
    "typescript": "5.0.0",
    "metro-react-native-babel-preset": "0.76.0"
  }
}
```

## UI/UX Design for TV

### Design Principles
1. **10-Foot Interface**: Optimized for viewing from 10 feet away
2. **Focus-Driven Navigation**: Clear focus indicators and smooth transitions
3. **Remote Control First**: All interactions via D-pad and select button
4. **Simplified Layout**: Reduced cognitive load with clear hierarchy
5. **High Contrast**: Excellent visibility in various lighting conditions

### Screen Layouts

#### Home Screen
```javascript
// Home screen with horizontal carousels
const HomeScreen = () => {
  return (
    <ScrollView style={styles.container}>
      <FeaturedContent />
      <ContentCarousel title="Continue Watching" data={continueWatching} />
      <ContentCarousel title="Trending Movies" data={trendingMovies} />
      <ContentCarousel title="Latest Series" data={latestSeries} />
      <ContentCarousel title="Anime" data={animeContent} />
      <ContentCarousel title="Live TV" data={liveChannels} />
    </ScrollView>
  );
};
```

#### Content Carousel Component
```javascript
const ContentCarousel = ({ title, data }) => {
  const [focusedIndex, setFocusedIndex] = useState(0);
  
  return (
    <View style={styles.carousel}>
      <Text style={styles.carouselTitle}>{title}</Text>
      <FlatList
        horizontal
        data={data}
        keyExtractor={(item) => item.id}
        renderItem={({ item, index }) => (
          <ContentCard
            item={item}
            focused={index === focusedIndex}
            onFocus={() => setFocusedIndex(index)}
          />
        )}
        showsHorizontalScrollIndicator={false}
      />
    </View>
  );
};
```

### TV-Specific Components

#### Focusable Content Card
```javascript
const ContentCard = ({ item, focused, onFocus, onPress }) => {
  return (
    <TouchableOpacity
      style={[
        styles.card,
        focused && styles.cardFocused
      ]}
      onFocus={onFocus}
      onPress={onPress}
      hasTVPreferredFocus={focused}
    >
      <FastImage
        source={{ uri: item.poster }}
        style={styles.cardImage}
        resizeMode="cover"
      />
      <View style={styles.cardOverlay}>
        <Text style={styles.cardTitle}>{item.title}</Text>
        <Text style={styles.cardMeta}>{item.year} • {item.genre}</Text>
      </View>
    </TouchableOpacity>
  );
};
```

#### TV Navigation Handler
```javascript
const useTVNavigation = () => {
  useEffect(() => {
    const handleKeyDown = (event) => {
      switch (event.keyCode) {
        case 37: // Left arrow
          navigateLeft();
          break;
        case 39: // Right arrow
          navigateRight();
          break;
        case 38: // Up arrow
          navigateUp();
          break;
        case 40: // Down arrow
          navigateDown();
          break;
        case 13: // Enter/OK
          handleSelect();
          break;
        case 8: // Back
          handleBack();
          break;
      }
    };

    TVEventHandler.addEventListener('keydown', handleKeyDown);
    return () => TVEventHandler.removeEventListener('keydown', handleKeyDown);
  }, []);
};
```

## Video Player Implementation

### React Native Video Configuration
```javascript
const VideoPlayer = ({ source, onBack }) => {
  const [paused, setPaused] = useState(false);
  const [currentTime, setCurrentTime] = useState(0);
  const [duration, setDuration] = useState(0);
  const [showControls, setShowControls] = useState(true);

  return (
    <View style={styles.playerContainer}>
      <Video
        source={{ uri: source }}
        style={styles.video}
        paused={paused}
        resizeMode="contain"
        onLoad={(data) => setDuration(data.duration)}
        onProgress={(data) => setCurrentTime(data.currentTime)}
        onError={(error) => console.error('Video error:', error)}
        controls={false}
        fullscreen={true}
      />
      
      {showControls && (
        <VideoControls
          paused={paused}
          onPlayPause={() => setPaused(!paused)}
          currentTime={currentTime}
          duration={duration}
          onSeek={(time) => videoRef.current?.seek(time)}
          onBack={onBack}
        />
      )}
    </View>
  );
};
```

### HLS Streaming Support
```javascript
// HLS stream handling
const getVideoSource = (streamUrl) => {
  if (streamUrl.includes('.m3u8')) {
    return {
      uri: streamUrl,
      type: 'm3u8'
    };
  }
  return { uri: streamUrl };
};

// Adaptive bitrate configuration
const videoConfig = {
  bufferConfig: {
    minBufferMs: 15000,
    maxBufferMs: 50000,
    bufferForPlaybackMs: 2500,
    bufferForPlaybackAfterRebufferMs: 5000
  },
  preferredAudioLanguage: 'en',
  preferredTextLanguage: 'en'
};
```

## API Integration

### GraphQL Client Setup
```javascript
import { ApolloClient, InMemoryCache, createHttpLink } from '@apollo/client';
import { setContext } from '@apollo/client/link/context';

const httpLink = createHttpLink({
  uri: 'https://netstream-backend.onrender.com/graphql',
});

const authLink = setContext(async (_, { headers }) => {
  const token = await getStoredToken();
  return {
    headers: {
      ...headers,
      authorization: token ? `Bearer ${token}` : "",
    }
  };
});

const client = new ApolloClient({
  link: authLink.concat(httpLink),
  cache: new InMemoryCache({
    typePolicies: {
      Query: {
        fields: {
          movies: {
            merge: false
          },
          series: {
            merge: false
          }
        }
      }
    }
  }),
  defaultOptions: {
    watchQuery: {
      errorPolicy: 'all'
    },
    query: {
      errorPolicy: 'all'
    }
  }
});
```

### Content Fetching Hooks
```javascript
// Custom hook for fetching content
const useContent = (type, page = 1) => {
  const [loading, setLoading] = useState(true);
  const [data, setData] = useState([]);
  const [error, setError] = useState(null);

  const { data: queryData, loading: queryLoading, error: queryError } = useQuery(
    GET_CONTENT_QUERY,
    {
      variables: { type, page, limit: 20 },
      fetchPolicy: 'cache-and-network'
    }
  );

  useEffect(() => {
    if (queryData) {
      setData(queryData[type.toLowerCase()]);
      setLoading(false);
    }
    if (queryError) {
      setError(queryError);
      setLoading(false);
    }
  }, [queryData, queryError]);

  return { data, loading, error };
};
```

### Authentication Service
```javascript
// TV-specific authentication
class AuthService {
  static async authenticateDevice() {
    try {
      // Generate device ID
      const deviceId = await DeviceInfo.getUniqueId();
      const deviceName = await DeviceInfo.getDeviceName();
      
      // Register device with backend
      const response = await client.mutate({
        mutation: REGISTER_DEVICE,
        variables: { deviceId, deviceName, platform: 'androidtv' }
      });
      
      return response.data.registerDevice;
    } catch (error) {
      throw new Error('Device authentication failed');
    }
  }

  static async pairWithQRCode(qrCode) {
    try {
      const response = await client.mutate({
        mutation: PAIR_DEVICE,
        variables: { qrCode }
      });
      
      const { token, user } = response.data.pairDevice;
      await this.storeToken(token);
      return user;
    } catch (error) {
      throw new Error('QR code pairing failed');
    }
  }
}
```

## Navigation System

### Stack Navigator Configuration
```javascript
import { createStackNavigator } from '@react-navigation/stack';

const Stack = createStackNavigator();

const AppNavigator = () => {
  return (
    <Stack.Navigator
      screenOptions={{
        headerShown: false,
        cardStyleInterpolator: ({ current, layouts }) => ({
          cardStyle: {
            transform: [
              {
                translateX: current.progress.interpolate({
                  inputRange: [0, 1],
                  outputRange: [layouts.screen.width, 0],
                }),
              },
            ],
          },
        }),
      }}
    >
      <Stack.Screen name="Home" component={HomeScreen} />
      <Stack.Screen name="Browse" component={BrowseScreen} />
      <Stack.Screen name="Details" component={DetailsScreen} />
      <Stack.Screen name="Player" component={PlayerScreen} />
      <Stack.Screen name="Search" component={SearchScreen} />
      <Stack.Screen name="Settings" component={SettingsScreen} />
    </Stack.Navigator>
  );
};
```

### Focus Management
```javascript
// Focus management system
class FocusManager {
  static focusableElements = new Map();
  static currentFocus = null;

  static registerFocusable(id, element, position) {
    this.focusableElements.set(id, { element, position });
  }

  static unregisterFocusable(id) {
    this.focusableElements.delete(id);
  }

  static moveFocus(direction) {
    const current = this.currentFocus;
    if (!current) return;

    const candidates = this.getFocusCandidates(current, direction);
    const next = this.findBestCandidate(candidates, direction);
    
    if (next) {
      this.setFocus(next);
    }
  }

  static setFocus(elementId) {
    const element = this.focusableElements.get(elementId);
    if (element) {
      element.element.focus();
      this.currentFocus = elementId;
    }
  }
}
```

## Performance Optimization

### Image Optimization
```javascript
// Optimized image loading for TV
const OptimizedImage = ({ source, style, ...props }) => {
  return (
    <FastImage
      source={{
        uri: source,
        priority: FastImage.priority.normal,
        cache: FastImage.cacheControl.immutable
      }}
      style={style}
      resizeMode={FastImage.resizeMode.cover}
      {...props}
    />
  );
};
```

### Memory Management
```javascript
// Memory optimization for large content lists
const VirtualizedContentList = ({ data, renderItem }) => {
  return (
    <VirtualizedList
      data={data}
      renderItem={renderItem}
      keyExtractor={(item) => item.id}
      getItemCount={() => data.length}
      getItem={(data, index) => data[index]}
      initialNumToRender={10}
      maxToRenderPerBatch={5}
      windowSize={10}
      removeClippedSubviews={true}
    />
  );
};
```

### Caching Strategy
```javascript
// Content caching for offline viewing
class ContentCache {
  static async cacheContent(contentId, streamUrl) {
    try {
      const downloadTask = RNFS.downloadFile({
        fromUrl: streamUrl,
        toFile: `${RNFS.DocumentDirectoryPath}/cache/${contentId}.mp4`,
        background: true,
        discretionary: true,
        cacheable: true
      });

      return downloadTask.promise;
    } catch (error) {
      console.error('Caching failed:', error);
    }
  }

  static async getCachedContent(contentId) {
    const filePath = `${RNFS.DocumentDirectoryPath}/cache/${contentId}.mp4`;
    const exists = await RNFS.exists(filePath);
    return exists ? filePath : null;
  }
}
```

## Development Timeline

### Phase 1: Foundation (Weeks 1-3)
**Week 1: Project Setup**
- [ ] Initialize React Native TV project
- [ ] Configure development environment
- [ ] Set up navigation structure
- [ ] Implement basic TV navigation

**Week 2: Core Components**
- [ ] Develop focusable components
- [ ] Implement content carousels
- [ ] Create TV-optimized layouts
- [ ] Set up GraphQL client

**Week 3: Authentication**
- [ ] Implement device registration
- [ ] Create QR code pairing flow
- [ ] Set up token management
- [ ] Test authentication flow

### Phase 2: Content & Player (Weeks 4-6)
**Week 4: Content Integration**
- [ ] Implement content fetching
- [ ] Create browse screens
- [ ] Add search functionality
- [ ] Implement content details

**Week 5: Video Player**
- [ ] Integrate React Native Video
- [ ] Implement HLS streaming
- [ ] Create player controls
- [ ] Add subtitle support

**Week 6: Player Enhancement**
- [ ] Implement seek functionality
- [ ] Add quality selection
- [ ] Create picture-in-picture
- [ ] Optimize playback performance

### Phase 3: Features & Polish (Weeks 7-9)
**Week 7: Advanced Features**
- [ ] Implement watchlist
- [ ] Add continue watching
- [ ] Create user profiles
- [ ] Implement parental controls

**Week 8: Performance & Testing**
- [ ] Optimize memory usage
- [ ] Implement caching
- [ ] Performance testing
- [ ] Bug fixes and polish

**Week 9: Final Testing**
- [ ] End-to-end testing
- [ ] Device compatibility testing
- [ ] Performance benchmarking
- [ ] User acceptance testing

### Phase 4: Deployment (Weeks 10-12)
**Week 10: Store Preparation**
- [ ] Create store assets
- [ ] Write app descriptions
- [ ] Prepare screenshots/videos
- [ ] Set up Google Play Console

**Week 11: Beta Testing**
- [ ] Internal testing
- [ ] Beta user recruitment
- [ ] Feedback collection
- [ ] Bug fixes

**Week 12: Launch**
- [ ] Final app submission
- [ ] Store review process
- [ ] Launch preparation
- [ ] Post-launch monitoring

## Testing Strategy

### Unit Testing
```javascript
// Component testing with React Native Testing Library
import { render, fireEvent } from '@testing-library/react-native';
import ContentCard from '../ContentCard';

describe('ContentCard', () => {
  it('should handle focus correctly', () => {
    const mockOnFocus = jest.fn();
    const { getByTestId } = render(
      <ContentCard
        item={mockItem}
        focused={false}
        onFocus={mockOnFocus}
      />
    );

    fireEvent(getByTestId('content-card'), 'focus');
    expect(mockOnFocus).toHaveBeenCalled();
  });
});
```

### Integration Testing
```javascript
// Navigation testing
import { NavigationContainer } from '@react-navigation/native';
import { render } from '@testing-library/react-native';
import AppNavigator from '../AppNavigator';

describe('Navigation', () => {
  it('should navigate between screens', async () => {
    const { getByTestId } = render(
      <NavigationContainer>
        <AppNavigator />
      </NavigationContainer>
    );

    // Test navigation flow
    fireEvent.press(getByTestId('browse-button'));
    expect(getByTestId('browse-screen')).toBeTruthy();
  });
});
```

### Device Testing
- **Android TV Emulator**: Primary development testing
- **Physical Devices**: NVIDIA Shield, Mi Box, Fire TV
- **Performance Testing**: Memory usage, CPU utilization
- **Network Testing**: Various connection speeds

## Deployment & Distribution

### Google Play Console Setup
```xml
<!-- Android TV app manifest -->
<manifest xmlns:android="http://schemas.android.com/apk/res/android">
    <uses-feature
        android:name="android.software.leanback"
        android:required="true" />
    <uses-feature
        android:name="android.hardware.touchscreen"
        android:required="false" />
    
    <application
        android:banner="@drawable/tv_banner"
        android:theme="@style/Theme.Leanback">
        
        <activity
            android:name=".MainActivity"
            android:exported="true"
            android:launchMode="singleTask"
            android:theme="@style/Theme.Leanback">
            <intent-filter>
                <action android:name="android.intent.action.MAIN" />
                <category android:name="android.intent.category.LEANBACK_LAUNCHER" />
            </intent-filter>
        </activity>
    </application>
</manifest>
```

### Store Assets Required
- **TV Banner**: 1280x720px
- **Feature Graphic**: 1024x500px
- **Screenshots**: 1920x1080px (minimum 2)
- **App Icon**: 512x512px
- **Promo Video**: 30 seconds maximum

### Release Process
1. **Alpha Testing**: Internal team testing
2. **Beta Testing**: Closed beta with 50-100 users
3. **Production Release**: Gradual rollout (10% → 50% → 100%)
4. **Post-Launch**: Monitor crash reports and user feedback

This comprehensive development plan provides a roadmap for creating a high-quality Android TV app that leverages the existing NetStream infrastructure while providing an optimal TV viewing experience.
