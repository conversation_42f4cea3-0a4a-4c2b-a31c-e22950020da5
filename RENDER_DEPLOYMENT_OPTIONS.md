# NetStream Render.com Deployment Options

## Overview

You have two main deployment options for NetStream on Render.com. Both maintain ALL your original features while optimizing for cloud deployment.

## Option 1: Combined Service (Recommended for Start) 💰

**Cost**: $14/month total
- Combined Service: $7/month
- Redis: $7/month

**Pros**:
- Lowest cost
- Simpler deployment
- No CORS issues between services
- Single service to manage

**Cons**:
- Single point of failure
- Harder to scale individual components

### Files Used:
- `Dockerfile.combined` - Builds frontend + backend in one container
- `server-combined.js` - Serves both frontend and backend
- `render.yaml` - Deployment configuration

## Option 2: Separate Services (Better for Production) 🚀

**Cost**: $21/month total
- Backend Service: $7/month
- Frontend Service: $7/month
- Redis: $7/month

**Pros**:
- Better separation of concerns
- Can scale frontend/backend independently
- More resilient (if one fails, other still works)
- Better for team development

**Cons**:
- Higher cost
- More complex CORS configuration
- Two services to manage

### Files Used:
- `Dockerfile` - Backend container
- `netstream-nextjs/Dockerfile` - Frontend container
- `server-fastify.js` - Backend server
- `render-separate.yaml` - Deployment configuration

## Quick Deployment Guide

### Option 1: Combined Service (Simplest)

1. **Go to [Render.com](https://render.com)** and sign up
2. **Click "New +" → "Blueprint"**
3. **Connect your GitHub repository**
4. **Select `render-deployment` branch**
5. **Use `render.yaml` file** (it will be detected automatically)
6. **Set these environment variables in Render dashboard**:
   ```bash
   MONGO_URI=mongodb+srv://crypto:<EMAIL>/NetStream?retryWrites=true&w=majority
   TMDB_API_KEY=94b3e867d01d17b6de1f13d5775bf60a
   GEMINI_API_KEY=your_gemini_key
   JWT_SECRET=your_secure_jwt_secret
   ```
7. **Deploy!** ✨

### Option 2: Separate Services

1. **Rename files**:
   ```bash
   mv render.yaml render-combined.yaml
   mv render-separate.yaml render.yaml
   ```
2. **Follow same steps as Option 1**
3. **Set environment variables for both services**

## Manual Deployment (Without render.yaml)

### Option 1: Combined Service

1. **Create Redis Service**:
   - Type: Redis
   - Name: `netstream-redis`
   - Plan: Starter

2. **Create Web Service**:
   - Type: Web Service
   - Name: `netstream-combined`
   - Environment: Docker
   - Dockerfile Path: `./Dockerfile.combined`
   - Build Command: (leave empty)
   - Start Command: `node server-combined.js`
   - Health Check Path: `/health`

3. **Set Environment Variables**:
   ```bash
   NODE_ENV=production
   PORT=3001
   HOST=0.0.0.0
   RENDER=true
   
   # Set these manually:
   MONGO_URI=your_mongodb_uri
   TMDB_API_KEY=your_tmdb_key
   GEMINI_API_KEY=your_gemini_key
   JWT_SECRET=your_jwt_secret
   
   # Auto-set by Render:
   REDIS_URL=(from Redis service)
   ```

### Option 2: Separate Services

1. **Create Redis Service** (same as above)

2. **Create Backend Service**:
   - Type: Web Service
   - Name: `netstream-backend`
   - Environment: Docker
   - Dockerfile Path: `./Dockerfile`
   - Start Command: `node server-fastify.js`
   - Health Check Path: `/health`

3. **Create Frontend Service**:
   - Type: Web Service
   - Name: `netstream-frontend`
   - Environment: Docker
   - Dockerfile Path: `./netstream-nextjs/Dockerfile`
   - Start Command: `node server.js`
   - Health Check Path: `/api/health`

4. **Set Environment Variables** (see render-separate.yaml for details)

## Testing Your Deployment

### Health Checks

**Combined Service**:
```bash
curl https://your-app-name.onrender.com/health
curl https://your-app-name.onrender.com/graphql
```

**Separate Services**:
```bash
curl https://netstream-backend.onrender.com/health
curl https://netstream-frontend.onrender.com/api/health
```

### Functional Testing

1. **Visit your app**: `https://your-app-name.onrender.com`
2. **Test GraphQL**: Visit `/graphql` endpoint
3. **Test features**:
   - Browse movies, series, anime
   - Search functionality
   - Video streaming
   - Admin panel (login required)
   - User registration/login

## Performance Expectations

### Combined Service
- **Memory**: ~800MB-1GB usage
- **Response Time**: 100-300ms
- **Concurrent Users**: 50-100

### Separate Services
- **Backend Memory**: ~600MB
- **Frontend Memory**: ~200MB
- **Response Time**: 50-200ms
- **Concurrent Users**: 100-200

## Troubleshooting

### Common Issues

1. **Build Fails**:
   - Check Dockerfile paths
   - Verify all dependencies in package.json
   - Check build logs in Render dashboard

2. **Service Won't Start**:
   - Check environment variables
   - Verify MongoDB connection string
   - Check health endpoint logs

3. **CORS Issues** (Separate Services):
   - Verify FRONTEND_URL is set correctly
   - Check CORS configuration in backend

4. **Frontend Can't Connect to Backend**:
   - Check NEXT_PUBLIC_API_URL environment variable
   - Verify backend service is running
   - Test GraphQL endpoint directly

### Performance Issues

1. **High Memory Usage**:
   - Monitor Puppeteer processes
   - Check for memory leaks
   - Consider upgrading to Standard plan

2. **Slow Response Times**:
   - Check database connection
   - Monitor Redis performance
   - Optimize GraphQL queries

## Scaling Recommendations

### Start With Combined Service
- Deploy with Option 1 (Combined)
- Monitor performance and costs
- Test all functionality

### Scale to Separate Services
- When you need better performance
- When you have more users
- When you want to scale components independently

### Upgrade Plans
- **Starter**: $7/month, 512MB RAM, 0.5 vCPU
- **Standard**: $25/month, 2GB RAM, 1 vCPU
- **Pro**: $85/month, 4GB RAM, 2 vCPU

## Which Option Should You Choose?

### Choose Combined Service If:
- You're just starting out
- You want to minimize costs
- You have low to moderate traffic
- You want simplicity

### Choose Separate Services If:
- You expect high traffic
- You want better reliability
- You plan to scale
- You have a team working on different parts

## Next Steps

1. **Choose your deployment option**
2. **Set up Render.com account**
3. **Deploy using the guide above**
4. **Test all functionality**
5. **Monitor performance**
6. **Scale as needed**

Both options maintain ALL your NetStream features while providing excellent cloud deployment optimization!
