# Render.com Deployment Optimization Plan

## Overview

This document outlines the optimizations needed to deploy the FULL NetStream application to Render.com while maintaining ALL existing features and functionality. The focus is on proper service communication, CORS configuration, environment setup, and performance optimization for cloud deployment.

## Current Architecture Analysis

### Services Required on Render.com
1. **Backend Service** (Fastify + GraphQL + Workers)
2. **Frontend Service** (Next.js)
3. **Redis Service** (Managed Redis)

### Key Optimization Areas

#### 1. Service Communication & URLs
- Configure proper inter-service communication
- Set up environment-specific API URLs
- Handle dynamic service URLs on Render.com

#### 2. CORS Configuration
- Configure CORS for cross-service communication
- Handle frontend-backend communication
- Set up proper origins for production

#### 3. Environment Configuration
- Production environment variables
- Service discovery and URL configuration
- Database and Redis connection optimization

#### 4. Docker Optimization
- Optimize Docker builds for faster deployment
- Reduce image sizes while keeping all features
- Improve build caching

#### 5. Performance Optimization
- Database connection pooling for cloud deployment
- Redis optimization for managed service
- Memory and CPU optimization for cloud instances

## Implementation Plan

### Phase 1: Service Communication Setup

#### Backend CORS Configuration
```javascript
// Enhanced CORS for Render.com deployment
await fastify.register(require('@fastify/cors'), {
  origin: [
    process.env.FRONTEND_URL || 'http://localhost:3000',
    /\.onrender\.com$/,
    'https://netstream-frontend.onrender.com',
    process.env.CUSTOM_DOMAIN || ''
  ].filter(Boolean),
  credentials: true,
  methods: ['GET', 'POST', 'PUT', 'DELETE', 'OPTIONS'],
  allowedHeaders: ['Content-Type', 'Authorization', 'X-Requested-With']
});
```

#### Frontend API Configuration
```javascript
// Dynamic API URL configuration
const getApiUrl = () => {
  if (typeof window !== 'undefined') {
    // Client-side
    return process.env.NEXT_PUBLIC_API_URL || 
           process.env.NEXT_PUBLIC_BACKEND_URL ||
           'https://netstream-backend.onrender.com';
  }
  // Server-side
  return process.env.API_URL || 
         process.env.BACKEND_URL ||
         'https://netstream-backend.onrender.com';
};
```

### Phase 2: Environment Configuration

#### Backend Environment Variables
```bash
# Service URLs
FRONTEND_URL=https://netstream-frontend.onrender.com
CORS_ORIGINS=https://netstream-frontend.onrender.com,https://your-custom-domain.com

# Database & Redis (existing)
MONGO_URI=mongodb+srv://...
REDIS_URL=redis://...

# API Keys (existing)
TMDB_API_KEY=...
GEMINI_API_KEY=...
TELEGRAM_TOKEN=...

# Performance Settings
NODE_ENV=production
FASTIFY_PORT=3001
MAX_CONCURRENT_PAGES=3
ENABLE_CACHING=true

# Render.com specific
RENDER=true
RENDER_SERVICE_NAME=netstream-backend
```

#### Frontend Environment Variables
```bash
# API Configuration
NEXT_PUBLIC_API_URL=https://netstream-backend.onrender.com/graphql
NEXT_PUBLIC_BACKEND_URL=https://netstream-backend.onrender.com
API_URL=https://netstream-backend.onrender.com/graphql

# Application Settings
NODE_ENV=production
NEXT_TELEMETRY_DISABLED=1

# Authentication
JWT_SECRET=... (same as backend)
```

### Phase 3: Docker Optimization

#### Optimized Backend Dockerfile
```dockerfile
# Multi-stage build for production optimization
FROM node:18-slim AS base
WORKDIR /app

# Install system dependencies for Puppeteer
RUN apt-get update && apt-get install -y \
    chromium \
    fonts-liberation \
    libasound2 \
    libatk-bridge2.0-0 \
    libdrm2 \
    libgtk-3-0 \
    libgtk-4-1 \
    libnss3 \
    libxcomposite1 \
    libxdamage1 \
    libxrandr2 \
    xdg-utils \
    curl \
    && rm -rf /var/lib/apt/lists/*

# Dependencies stage
FROM base AS deps
COPY package*.json ./
RUN npm ci --only=production && npm cache clean --force

# Production stage
FROM base AS production
COPY --from=deps /app/node_modules ./node_modules
COPY . .

# Set Puppeteer to use installed Chromium
ENV PUPPETEER_EXECUTABLE_PATH=/usr/bin/chromium

# Create non-root user
RUN groupadd -r netstream && useradd -r -g netstream netstream
RUN chown -R netstream:netstream /app
USER netstream

EXPOSE 3001

# Health check
HEALTHCHECK --interval=30s --timeout=10s --start-period=60s --retries=3 \
  CMD curl -f http://localhost:3001/health || exit 1

CMD ["node", "server-fastify.js"]
```

#### Optimized Frontend Dockerfile
```dockerfile
# Multi-stage build for Next.js
FROM node:18-alpine AS base
WORKDIR /app

# Dependencies
FROM base AS deps
RUN apk add --no-cache libc6-compat
COPY package*.json ./
RUN npm ci --only=production

# Builder
FROM base AS builder
COPY --from=deps /app/node_modules ./node_modules
COPY . .

# Build with production environment
ENV NODE_ENV=production
ENV NEXT_TELEMETRY_DISABLED=1

RUN npm run build

# Production image
FROM base AS runner
WORKDIR /app

ENV NODE_ENV=production
ENV NEXT_TELEMETRY_DISABLED=1

RUN addgroup --system --gid 1001 nodejs
RUN adduser --system --uid 1001 nextjs

COPY --from=builder /app/public ./public
COPY --from=builder --chown=nextjs:nodejs /app/.next/standalone ./
COPY --from=builder --chown=nextjs:nodejs /app/.next/static ./.next/static

USER nextjs

EXPOSE 3000

ENV PORT=3000
ENV HOSTNAME="0.0.0.0"

CMD ["node", "server.js"]
```

### Phase 4: Service Configuration Files

#### render.yaml for Complete Deployment
```yaml
services:
  # Redis Service
  - type: redis
    name: netstream-redis
    plan: starter
    maxmemoryPolicy: allkeys-lru

  # Backend Service
  - type: web
    name: netstream-backend
    env: docker
    dockerfilePath: ./Dockerfile
    plan: starter
    region: oregon
    buildCommand: echo "Building with Docker"
    startCommand: node server-fastify.js
    healthCheckPath: /health
    autoDeploy: true
    envVars:
      - key: NODE_ENV
        value: production
      - key: FASTIFY_PORT
        value: 3001
      - key: RENDER
        value: true
      - key: FRONTEND_URL
        fromService:
          type: web
          name: netstream-frontend
          property: host
      - key: REDIS_URL
        fromService:
          type: redis
          name: netstream-redis
          property: connectionString

  # Frontend Service
  - type: web
    name: netstream-frontend
    env: docker
    dockerfilePath: ./netstream-nextjs/Dockerfile
    plan: starter
    region: oregon
    buildCommand: echo "Building with Docker"
    startCommand: node server.js
    healthCheckPath: /api/health
    autoDeploy: true
    envVars:
      - key: NODE_ENV
        value: production
      - key: NEXT_TELEMETRY_DISABLED
        value: 1
      - key: NEXT_PUBLIC_API_URL
        fromService:
          type: web
          name: netstream-backend
          property: host
          envVarKey: GRAPHQL_ENDPOINT
```

### Phase 5: Application Code Optimizations

#### Enhanced Health Checks
```javascript
// Enhanced health check for Render.com
fastify.get('/health', async (request, reply) => {
  const health = {
    status: 'healthy',
    timestamp: new Date().toISOString(),
    service: 'netstream-backend',
    version: process.env.npm_package_version || '1.0.0',
    uptime: process.uptime(),
    memory: process.memoryUsage(),
    environment: process.env.NODE_ENV,
    render: !!process.env.RENDER
  };

  // Check database connection
  try {
    await db.admin().ping();
    health.database = 'connected';
  } catch (error) {
    health.database = 'disconnected';
    health.status = 'degraded';
  }

  // Check Redis connection
  try {
    await redis.ping();
    health.redis = 'connected';
  } catch (error) {
    health.redis = 'disconnected';
    health.status = 'degraded';
  }

  const statusCode = health.status === 'healthy' ? 200 : 503;
  return reply.code(statusCode).send(health);
});
```

#### Database Connection Optimization
```javascript
// Optimized MongoDB connection for Render.com
const mongoOptions = {
  maxPoolSize: 20, // Increased for cloud deployment
  minPoolSize: 5,
  maxIdleTimeMS: 30000,
  serverSelectionTimeoutMS: 10000, // Increased for cloud latency
  socketTimeoutMS: 45000,
  family: 4,
  keepAlive: true,
  keepAliveInitialDelay: 300000,
  retryWrites: true,
  w: 'majority',
  // Cloud-specific optimizations
  compressors: ['zlib'],
  zlibCompressionLevel: 6,
  readPreference: 'primaryPreferred',
  readConcern: { level: 'majority' }
};
```

#### Redis Connection Optimization
```javascript
// Optimized Redis connection for Render.com managed Redis
const redisOptions = {
  host: redisHost,
  port: redisPort,
  password: redisPassword,
  // Cloud optimizations
  connectTimeout: 10000,
  commandTimeout: 5000,
  retryDelayOnFailover: 100,
  enableReadyCheck: false,
  maxLoadingTimeout: 5000,
  lazyConnect: true,
  keepAlive: true,
  family: 4,
  maxRetriesPerRequest: 3,
  // Render.com specific
  tls: process.env.REDIS_TLS === 'true' ? {} : undefined
};
```

## Deployment Steps

### 1. Environment Setup
- Set up Render.com account
- Configure environment variables
- Set up custom domains (optional)

### 2. Service Deployment Order
1. Deploy Redis service first
2. Deploy backend service (will get Redis URL automatically)
3. Deploy frontend service (will get backend URL automatically)

### 3. Post-Deployment Configuration
- Verify service communication
- Test all features end-to-end
- Configure monitoring and alerts
- Set up custom domains if needed

## Expected Performance

### Resource Usage
- **Backend**: 1GB RAM, 1 vCPU (Starter plan)
- **Frontend**: 512MB RAM, 0.5 vCPU (Starter plan)
- **Redis**: 25MB memory (Starter plan)

### Response Times
- **API Responses**: 50-200ms
- **Page Load**: 1-3 seconds
- **Search**: 200-500ms
- **Video Streaming**: Immediate (direct links)

### Costs
- **Backend**: $7/month
- **Frontend**: $7/month
- **Redis**: $7/month
- **Total**: $21/month

This optimization maintains ALL existing features while ensuring proper cloud deployment on Render.com.
