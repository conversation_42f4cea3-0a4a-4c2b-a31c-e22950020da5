# Render.com Deployment Guide for NetStream

## Pre-Deployment Checklist

### 1. Repository Preparation
- [ ] Ensure all sensitive data is in environment variables
- [ ] Verify Docker builds work locally
- [ ] Test health check endpoints
- [ ] Optimize Docker images for production
- [ ] Update CORS settings for production domains

### 2. Environment Variables Setup
Create a `.env.production` file with all required variables:

```bash
# Database Configuration
MONGO_URI=mongodb+srv://username:<EMAIL>/NetStream?retryWrites=true&w=majority

# Redis Configuration (will be provided by <PERSON>der)
REDIS_URL=redis://red-xxxxx:6379

# API Keys
TMDB_API_KEY=your_tmdb_api_key_here
GEMINI_API_KEY=your_gemini_api_key_here
TELEGRAM_TOKEN=your_telegram_bot_token
TELEGRAM_API_ID=your_telegram_api_id
TELEGRAM_API_HASH=your_telegram_api_hash

# Application Configuration
NODE_ENV=production
FASTIFY_PORT=3001
ADMIN_KEY=your_secure_admin_key
JWT_SECRET=your_jwt_secret_key

# Performance Settings
MAX_CONCURRENT_PAGES=2
MAX_RETRY_ATTEMPTS=3
RETRY_DELAY_BASE=3000
MAX_CONCURRENT_PUPPETEER_TASKS=1
MAX_CONCURRENT_AXIOS_TASKS=2

# Rate Limits (requests per minute)
GEMINI_RATE_LIMIT=28
TMDB_RATE_LIMIT=40
JIKAN_RATE_LIMIT=50

# Feature Flags
ENABLE_CACHING=true
USE_ADVANCED_ENRICHMENT=true
FETCH_SEASONS=true
SCRAPE_ON_START=false
SCRAPE_MODE=latest

# Telegram Configuration
PRIVATE_GROUP_ID=your_private_group_id
WIFLIX_CHANNEL=@your_channel

# External APIs
ONEUPLOAD_API_KEY=your_oneupload_key
```

## Step-by-Step Deployment

### Step 1: Create Render Account and Services

#### 1.1 Sign up for Render.com
- Go to [render.com](https://render.com)
- Sign up with GitHub account
- Connect your NetStream repository

#### 1.2 Create Redis Service
```yaml
Service Type: Redis
Name: netstream-redis
Plan: Starter ($7/month)
Region: Oregon (US West) or Frankfurt (EU)
Max Memory: 25MB
Eviction Policy: allkeys-lru
```

**Redis Configuration**:
- Enable persistence for data durability
- Set up monitoring alerts
- Note the Redis URL for backend configuration

### Step 2: Deploy Backend Service

#### 2.1 Create Web Service
```yaml
Service Type: Web Service
Name: netstream-backend
Repository: your-github-username/NetStream_graphql
Branch: main
Root Directory: . (leave empty)
Environment: Docker
```

#### 2.2 Build Configuration
```yaml
Build Command: docker build -f Dockerfile .
Start Command: node server-fastify.js
```

#### 2.3 Environment Variables
Add all environment variables from the checklist above:

**Critical Variables**:
```bash
MONGO_URI=mongodb+srv://...
REDIS_URL=redis://red-xxxxx:6379  # From Redis service
NODE_ENV=production
FASTIFY_PORT=3001
ADMIN_KEY=your_secure_admin_key
```

#### 2.4 Health Check Configuration
```yaml
Health Check Path: /health
Health Check Grace Period: 60 seconds
```

#### 2.5 Advanced Settings
```yaml
Auto-Deploy: Yes
Instance Type: Starter ($7/month)
Region: Same as Redis service
```

### Step 3: Deploy Frontend Service

#### 3.1 Create Web Service
```yaml
Service Type: Web Service
Name: netstream-frontend
Repository: your-github-username/NetStream_graphql
Branch: main
Root Directory: netstream-nextjs
Environment: Docker
```

#### 3.2 Build Configuration
```yaml
Build Command: docker build -f ../Dockerfile.frontend .
Start Command: node server.js
```

#### 3.3 Environment Variables
```bash
# API Configuration
API_URL=https://netstream-backend.onrender.com/graphql
NEXT_PUBLIC_API_URL=https://netstream-backend.onrender.com/graphql
NEXT_PUBLIC_API_BASE_URL=https://netstream-backend.onrender.com
NEXT_PUBLIC_GRAPHQL_ENDPOINT=https://netstream-backend.onrender.com/graphql

# Application Settings
NODE_ENV=production
NEXT_TELEMETRY_DISABLED=1

# Authentication
JWT_SECRET=same_as_backend_jwt_secret

# Build Arguments
MONGO_URI=mongodb+srv://...  # Required for build
```

#### 3.4 Health Check Configuration
```yaml
Health Check Path: /api/health
Health Check Grace Period: 90 seconds
```

### Step 4: Configure Custom Domains (Optional)

#### 4.1 Backend Domain
```yaml
Custom Domain: api.yourapp.com
SSL Certificate: Auto-generated (Let's Encrypt)
```

#### 4.2 Frontend Domain
```yaml
Custom Domain: yourapp.com
SSL Certificate: Auto-generated (Let's Encrypt)
```

#### 4.3 Update Environment Variables
After setting custom domains, update:
```bash
# Frontend environment variables
API_URL=https://api.yourapp.com/graphql
NEXT_PUBLIC_API_URL=https://api.yourapp.com/graphql
NEXT_PUBLIC_API_BASE_URL=https://api.yourapp.com
```

### Step 5: Database Setup

#### 5.1 MongoDB Atlas Configuration
Ensure your MongoDB Atlas cluster allows connections from Render:

**IP Whitelist**:
- Add `0.0.0.0/0` for Render's dynamic IPs
- Or use specific Render IP ranges if available

**Connection String**:
```bash
mongodb+srv://username:<EMAIL>/NetStream?retryWrites=true&w=majority&ssl=true
```

#### 5.2 Database Indexes
After deployment, create indexes via MongoDB Compass or shell:

```javascript
// Text search indexes
db.movies.createIndex({
  "title": "text",
  "cleanedTitle": "text",
  "metadata.actors": "text",
  "tmdb.title": "text"
}, {
  weights: { title: 10, cleanedTitle: 8, "tmdb.title": 6, "metadata.actors": 2 },
  name: "text_search_index"
});

// Performance indexes
db.movies.createIndex({ "updatedAt": -1 });
db.movies.createIndex({ "tmdb.id": 1 });
db.movies.createIndex({ "tmdb.genres": 1 });
db.series.createIndex({ "updatedAt": -1 });
db.animes.createIndex({ "updatedAt": -1 });
db.trending_items.createIndex({ "mediaType": 1, "rank": 1 });
```

### Step 6: Post-Deployment Configuration

#### 6.1 Verify Services
Check that all services are running:
- [ ] Backend health check: `https://netstream-backend.onrender.com/health`
- [ ] Frontend health check: `https://netstream-frontend.onrender.com/api/health`
- [ ] Redis connectivity test
- [ ] Database connection test

#### 6.2 Test Core Functionality
- [ ] GraphQL playground accessible
- [ ] User registration/login works
- [ ] Content browsing functional
- [ ] Video streaming works
- [ ] Admin panel accessible
- [ ] Search functionality works

#### 6.3 Configure Monitoring
Set up monitoring alerts in Render dashboard:
- [ ] Service downtime alerts
- [ ] High memory usage alerts
- [ ] High CPU usage alerts
- [ ] Failed deployment alerts

### Step 7: Performance Optimization

#### 7.1 Enable Compression
Verify Fastify compression is working:
```javascript
// In server-fastify.js
await fastify.register(require('@fastify/compress'), {
  global: true,
  threshold: 1024
});
```

#### 7.2 Configure Caching Headers
```javascript
// Static asset caching
fastify.register(require('@fastify/static'), {
  root: path.join(__dirname, 'public'),
  prefix: '/public/',
  setHeaders: (res, path) => {
    if (path.endsWith('.js') || path.endsWith('.css')) {
      res.setHeader('Cache-Control', 'public, max-age=31536000');
    }
  }
});
```

#### 7.3 Database Connection Optimization
```javascript
// Optimized for Render's network
const mongoOptions = {
  maxPoolSize: 20,
  minPoolSize: 5,
  maxIdleTimeMS: 30000,
  serverSelectionTimeoutMS: 10000,
  socketTimeoutMS: 45000,
  bufferMaxEntries: 0,
  useNewUrlParser: true,
  useUnifiedTopology: true
};
```

## Troubleshooting Common Issues

### Issue 1: Build Failures

**Symptom**: Docker build fails during deployment
**Solutions**:
```bash
# Check Dockerfile syntax
docker build -f Dockerfile .

# Verify all dependencies are in package.json
npm audit

# Check for missing environment variables
grep -r "process.env" src/
```

### Issue 2: Memory Issues

**Symptom**: Service crashes with out-of-memory errors
**Solutions**:
```javascript
// Reduce Puppeteer concurrency
MAX_CONCURRENT_PUPPETEER_TASKS=1

// Optimize garbage collection
node --max-old-space-size=1024 server-fastify.js

// Reduce cache sizes
this.maxSize = 1000; // Reduce from 10000
```

### Issue 3: Database Connection Issues

**Symptom**: Cannot connect to MongoDB
**Solutions**:
```bash
# Check connection string format
MONGO_URI=mongodb+srv://user:<EMAIL>/db?retryWrites=true&w=majority

# Verify IP whitelist in MongoDB Atlas
# Add 0.0.0.0/0 for Render

# Test connection locally
node -e "require('mongodb').MongoClient.connect(process.env.MONGO_URI).then(() => console.log('Connected'))"
```

### Issue 4: Redis Connection Issues

**Symptom**: Cache not working, Redis errors
**Solutions**:
```bash
# Verify Redis URL format
REDIS_URL=redis://red-xxxxx:6379

# Check Redis service status in Render dashboard
# Restart Redis service if needed

# Test Redis connection
redis-cli -u $REDIS_URL ping
```

### Issue 5: CORS Issues

**Symptom**: Frontend cannot access backend API
**Solutions**:
```javascript
// Update CORS configuration
await fastify.register(require('@fastify/cors'), {
  origin: [
    'https://netstream-frontend.onrender.com',
    'https://yourapp.com',
    /\.onrender\.com$/
  ],
  credentials: true
});
```

## Monitoring and Maintenance

### Daily Monitoring
- [ ] Check service status in Render dashboard
- [ ] Monitor error rates and response times
- [ ] Verify scraping jobs are running
- [ ] Check database and Redis usage

### Weekly Maintenance
- [ ] Review application logs
- [ ] Monitor resource usage trends
- [ ] Check for dependency updates
- [ ] Verify backup integrity

### Monthly Tasks
- [ ] Security updates
- [ ] Performance optimization review
- [ ] Cost analysis and optimization
- [ ] Capacity planning review

## Scaling Considerations

### When to Scale Up
- CPU usage consistently > 80%
- Memory usage consistently > 85%
- Response times > 2 seconds
- Error rates > 1%

### Scaling Options
```yaml
# Upgrade to Standard plan
Instance Type: Standard ($25/month)
CPU: 1 vCPU
Memory: 2GB RAM

# Upgrade to Pro plan
Instance Type: Pro ($85/month)
CPU: 2 vCPU
Memory: 4GB RAM
```

### Horizontal Scaling
For high traffic, consider:
- Multiple backend instances with load balancer
- Database read replicas
- Redis cluster setup
- CDN for static assets

## Security Checklist

### Pre-Production Security
- [ ] All secrets in environment variables
- [ ] HTTPS enforced for all endpoints
- [ ] Rate limiting configured
- [ ] Input validation implemented
- [ ] SQL injection prevention
- [ ] XSS protection enabled

### Production Security
- [ ] Regular security updates
- [ ] Monitor for suspicious activity
- [ ] Backup encryption enabled
- [ ] Access logs reviewed
- [ ] API key rotation schedule

## Cost Optimization

### Current Costs (Monthly)
- Backend Service (Starter): $7
- Frontend Service (Starter): $7
- Redis Service (Starter): $7
- **Total**: $21/month

### Cost Reduction Strategies
- Use single service for both frontend/backend
- Optimize Docker images for smaller size
- Implement efficient caching to reduce compute
- Monitor and optimize database queries
- Use CDN for static assets

### Cost Monitoring
- Set up billing alerts in Render
- Monitor resource usage trends
- Regular cost analysis and optimization
- Consider reserved instances for predictable workloads

This deployment guide provides a comprehensive roadmap for successfully deploying NetStream to Render.com with optimal performance and reliability.
