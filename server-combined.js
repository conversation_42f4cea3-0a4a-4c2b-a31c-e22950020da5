// Combined Frontend + Backend Server for Render.com
// Serves Next.js frontend and Fastify GraphQL backend from single service

require("dotenv").config();

// Debug environment variables on startup
console.log('🔍 Environment Debug:', {
  NODE_ENV: process.env.NODE_ENV,
  PORT: process.env.PORT,
  HOST: process.env.HOST,
  MONGO_URI: process.env.MONGO_URI ? `${process.env.MONGO_URI.substring(0, 20)}...` : 'NOT SET',
  RENDER: process.env.RENDER,
  RENDER_SERVICE_NAME: process.env.RENDER_SERVICE_NAME
});
const fastify = require('fastify')({
  logger: {
    level: process.env.NODE_ENV === 'production' ? 'info' : 'debug',
    transport: process.env.NODE_ENV !== 'production' ? {
      target: 'pino-pretty',
      options: {
        colorize: true,
        translateTime: 'HH:MM:ss Z',
        ignore: 'pid,hostname'
      }
    } : undefined
  },
  trustProxy: true,
  bodyLimit: 1048576 * 10, // 10MB
  keepAliveTimeout: 30000,
  connectionTimeout: 30000,
  requestTimeout: 30000,
  disableRequestLogging: process.env.NODE_ENV === 'production'
});

const path = require('path');
const { MongoClient } = require('mongodb');
const fs = require('fs');

// Import all the existing backend functionality
const { mongoUri, port, host } = require('./src/config/env');

// Global variables for database and cache
let db;
let client;
let cacheService;

// Initialize database connection
async function connectToDatabase() {
  try {
    console.log('🔗 Attempting MongoDB connection...');
    console.log('📍 MongoDB URI format check:', mongoUri ? 'URI provided' : 'URI missing');

    if (!mongoUri) {
      throw new Error('MONGO_URI environment variable is not set');
    }

    client = new MongoClient(mongoUri, {
      maxPoolSize: 20,
      minPoolSize: 5,
      maxIdleTimeMS: 30000,
      serverSelectionTimeoutMS: 10000,
      socketTimeoutMS: 45000,
      family: 4,
      keepAlive: true,
      keepAliveInitialDelay: 300000,
      retryWrites: true,
      w: 'majority',
      compressors: ['zlib'],
      zlibCompressionLevel: 6,
      readPreference: 'primaryPreferred',
      readConcern: { level: 'majority' }
    });

    await client.connect();
    db = client.db();
    fastify.log.info('Connected to MongoDB');
    
    fastify.decorate('db', db);
    return db;
  } catch (error) {
    fastify.log.error('MongoDB connection error:', {
      message: error.message,
      code: error.code,
      name: error.name,
      mongoUri: mongoUri ? 'SET' : 'NOT SET',
      stack: error.stack
    });
    throw error;
  }
}

// Initialize cache service
async function initializeCacheService() {
  try {
    const CacheService = require('./src/cache/fastifyCache');
    cacheService = new CacheService();
    await cacheService.initialize();
    fastify.decorate('cacheService', cacheService);
    fastify.log.info('Cache service initialized');
  } catch (error) {
    fastify.log.warn('Cache service initialization failed:', error.message);
    fastify.decorate('cacheService', null);
  }
}

// Register plugins and routes
async function registerPlugins() {
  // Enhanced CORS for combined service
  await fastify.register(require('@fastify/cors'), {
    origin: true, // Allow all origins for combined service
    credentials: true,
    methods: ['GET', 'POST', 'PUT', 'DELETE', 'OPTIONS', 'PATCH'],
    allowedHeaders: ['Content-Type', 'Authorization', 'X-Requested-With', 'Accept', 'Origin']
  });

  // Compression
  await fastify.register(require('@fastify/compress'), {
    global: true,
    threshold: 1024,
    encodings: ['gzip', 'deflate']
  });

  // Serve Next.js static files
  await fastify.register(require('@fastify/static'), {
    root: path.join(__dirname, 'netstream-nextjs/.next/static'),
    prefix: '/_next/static/',
    decorateReply: false
  });

  // Serve Next.js public files
  await fastify.register(require('@fastify/static'), {
    root: path.join(__dirname, 'netstream-nextjs/public'),
    prefix: '/public/',
    decorateReply: false
  });

  // GraphQL endpoint
  const schema = fs.readFileSync(path.join(__dirname, 'schema.graphql'), 'utf8');
  const resolvers = require('./src/graphql/fastifyResolvers');

  await fastify.register(require('mercurius'), {
    schema,
    resolvers,
    graphiql: process.env.NODE_ENV !== 'production',
    jit: 1,
    cache: true,
    queryDepth: 12,
    context: (request, reply) => ({
      db: fastify.db,
      cache: fastify.cacheService,
      logger: fastify.log
    })
  });
}

// Register API routes
async function registerRoutes() {
  // Health check endpoint
  fastify.get('/health', async (request, reply) => {
    const startTime = Date.now();
    const health = {
      status: 'healthy',
      timestamp: new Date().toISOString(),
      service: 'netstream-combined',
      version: process.env.npm_package_version || '2.0.0',
      uptime: process.uptime(),
      memory: process.memoryUsage(),
      environment: process.env.NODE_ENV || 'development',
      render: !!process.env.RENDER,
      checks: {}
    };

    try {
      // Check database connection
      const dbStart = Date.now();
      await db.admin().ping();
      health.checks.database = {
        status: 'connected',
        responseTime: Date.now() - dbStart
      };
    } catch (error) {
      health.status = 'degraded';
      health.checks.database = {
        status: 'disconnected',
        error: error.message
      };
    }

    try {
      // Check cache connection
      if (cacheService) {
        const cacheStart = Date.now();
        const cacheHealth = await cacheService.healthCheck();
        health.checks.cache = {
          status: cacheHealth.status,
          responseTime: Date.now() - cacheStart,
          stats: cacheHealth.stats || {}
        };
      } else {
        health.checks.cache = { status: 'disabled' };
      }
    } catch (error) {
      health.status = 'degraded';
      health.checks.cache = {
        status: 'error',
        error: error.message
      };
    }

    // Add performance metrics
    health.performance = {
      totalResponseTime: Date.now() - startTime,
      memoryUsage: {
        rss: `${Math.round(health.memory.rss / 1024 / 1024)}MB`,
        heapTotal: `${Math.round(health.memory.heapTotal / 1024 / 1024)}MB`,
        heapUsed: `${Math.round(health.memory.heapUsed / 1024 / 1024)}MB`,
        external: `${Math.round(health.memory.external / 1024 / 1024)}MB`
      }
    };

    const statusCode = health.status === 'healthy' ? 200 : 503;
    return reply.code(statusCode).send(health);
  });

  // API info endpoint
  fastify.get('/api', async (request, reply) => {
    return {
      name: 'NetStream Combined API',
      version: '2.0.0',
      framework: 'Fastify + Next.js',
      graphql: '/graphql',
      health: '/health',
      timestamp: new Date().toISOString()
    };
  });

  // Frontend API health check
  fastify.get('/api/health', async (request, reply) => {
    return reply.redirect('/health');
  });

  // Catch-all for Next.js pages (SPA routing)
  fastify.get('*', async (request, reply) => {
    // Skip API routes and static files
    if (request.url.startsWith('/graphql') || 
        request.url.startsWith('/api') || 
        request.url.startsWith('/_next') || 
        request.url.startsWith('/public')) {
      return reply.code(404).send({ error: 'Not found' });
    }

    // Serve Next.js index.html for all other routes
    const indexPath = path.join(__dirname, 'netstream-nextjs/.next/server/pages/index.html');
    
    if (fs.existsSync(indexPath)) {
      const html = fs.readFileSync(indexPath, 'utf8');
      return reply.type('text/html').send(html);
    } else {
      // Fallback to a simple HTML page
      const fallbackHtml = `
        <!DOCTYPE html>
        <html>
          <head>
            <title>NetStream</title>
            <meta charset="utf-8">
            <meta name="viewport" content="width=device-width, initial-scale=1">
          </head>
          <body>
            <div id="__next">
              <h1>NetStream</h1>
              <p>Loading...</p>
              <p>GraphQL API available at <a href="/graphql">/graphql</a></p>
              <p>Health check at <a href="/health">/health</a></p>
            </div>
          </body>
        </html>
      `;
      return reply.type('text/html').send(fallbackHtml);
    }
  });
}

// Graceful shutdown
async function gracefulShutdown() {
  try {
    if (cacheService) {
      await cacheService.close();
      fastify.log.info('Cache service closed');
    }
    if (client) {
      await client.close();
      fastify.log.info('MongoDB connection closed');
    }
    await fastify.close();
    fastify.log.info('Server closed');
    process.exit(0);
  } catch (error) {
    fastify.log.error('Error during shutdown:', error);
    process.exit(1);
  }
}

process.on('SIGTERM', gracefulShutdown);
process.on('SIGINT', gracefulShutdown);

// Start server
async function start() {
  try {
    // Connect to database
    await connectToDatabase();
    
    // Initialize cache
    await initializeCacheService();
    
    // Register plugins and routes
    await registerPlugins();
    await registerRoutes();
    
    // Start listening
    await fastify.listen({ port: port, host: host });
    
    fastify.log.info(`🚀 NetStream Combined server listening on ${host}:${port}`);
    fastify.log.info(`📊 Health check: http://${host}:${port}/health`);
    fastify.log.info(`🔗 GraphQL endpoint: http://${host}:${port}/graphql`);
    fastify.log.info(`🎨 Frontend: http://${host}:${port}/`);
    
    if (process.env.RENDER) {
      fastify.log.info(`🌐 Render.com deployment detected`);
      fastify.log.info(`📦 Service: ${process.env.RENDER_SERVICE_NAME || 'netstream-combined'}`);
    }
  } catch (err) {
    fastify.log.error('Error starting server:', {
      message: err.message,
      code: err.code,
      name: err.name,
      stack: err.stack,
      env: {
        NODE_ENV: process.env.NODE_ENV,
        PORT: process.env.PORT,
        HOST: process.env.HOST,
        MONGO_URI: process.env.MONGO_URI ? 'SET' : 'NOT SET',
        RENDER: process.env.RENDER
      }
    });
    process.exit(1);
  }
}

// Handle uncaught exceptions
process.on('uncaughtException', (error) => {
  fastify.log.error('Uncaught Exception:', error);
  gracefulShutdown();
});

process.on('unhandledRejection', (reason, promise) => {
  fastify.log.error('Unhandled Rejection at:', promise, 'reason:', reason);
  gracefulShutdown();
});

start();
