// Combined Frontend + Backend Server for Render.com
// Serves Next.js frontend and Fastify GraphQL backend from single service

require("dotenv").config();

// Debug environment variables on startup
console.log('🔍 Environment Debug:', {
  NODE_ENV: process.env.NODE_ENV,
  PORT: process.env.PORT,
  HOST: process.env.HOST,
  MONGO_URI: process.env.MONGO_URI ? `${process.env.MONGO_URI.substring(0, 20)}...` : 'NOT SET',
  RENDER: process.env.RENDER,
  RENDER_SERVICE_NAME: process.env.RENDER_SERVICE_NAME
});
const fastify = require('fastify')({
  logger: {
    level: process.env.NODE_ENV === 'production' ? 'info' : 'debug',
    transport: process.env.NODE_ENV !== 'production' ? {
      target: 'pino-pretty',
      options: {
        colorize: true,
        translateTime: 'HH:MM:ss Z',
        ignore: 'pid,hostname'
      }
    } : undefined
  },
  trustProxy: true,
  bodyLimit: 1048576 * 10, // 10MB
  keepAliveTimeout: 30000,
  connectionTimeout: 30000,
  requestTimeout: 30000,
  disableRequestLogging: process.env.NODE_ENV === 'production'
});

const path = require('path');
const { MongoClient } = require('mongodb');
const fs = require('fs');

// Import all the existing backend functionality
const { mongoUri, port, host } = require('./src/config/env');

// Global variables for database and cache
let db;
let client;
let cacheService;

// Initialize database connection
async function connectToDatabase() {
  try {
    console.log('🔗 Attempting MongoDB connection...');
    console.log('📍 MongoDB URI format check:', mongoUri ? 'URI provided' : 'URI missing');

    if (!mongoUri) {
      throw new Error('MONGO_URI environment variable is not set');
    }

    client = new MongoClient(mongoUri, {
      maxPoolSize: 10,
      minPoolSize: 2,
      maxIdleTimeMS: 30000,
      serverSelectionTimeoutMS: 5000,
      socketTimeoutMS: 30000,
      connectTimeoutMS: 10000,
      retryWrites: true,
      w: 'majority',
      readPreference: 'primaryPreferred'
    });

    console.log('🔄 Connecting to MongoDB...');
    await client.connect();
    console.log('✅ MongoDB client connected');

    db = client.db();
    console.log('✅ Database instance obtained');

    // Test the connection with a simple operation
    await db.admin().ping();
    console.log('✅ MongoDB ping successful');

    fastify.log.info('Connected to MongoDB');
    
    fastify.decorate('db', db);
    return db;
  } catch (error) {
    console.error('❌ MongoDB connection failed:', {
      message: error.message,
      code: error.code,
      name: error.name,
      mongoUri: mongoUri ? 'SET' : 'NOT SET'
    });
    console.error('❌ Full error:', error);
    fastify.log.error('MongoDB connection error:', error.message);
    throw error;
  }
}

// Initialize cache service
async function initializeCacheService() {
  try {
    const CacheService = require('./src/cache/fastifyCache');
    cacheService = new CacheService();
    await cacheService.initialize();
    fastify.decorate('cacheService', cacheService);
    fastify.log.info('Cache service initialized');
  } catch (error) {
    fastify.log.warn('Cache service initialization failed:', error.message);
    fastify.decorate('cacheService', null);
  }
}

// Register plugins and routes
async function registerPlugins() {
  // Enhanced CORS for combined service
  await fastify.register(require('@fastify/cors'), {
    origin: true, // Allow all origins for combined service
    credentials: true,
    methods: ['GET', 'POST', 'PUT', 'DELETE', 'OPTIONS', 'PATCH'],
    allowedHeaders: ['Content-Type', 'Authorization', 'X-Requested-With', 'Accept', 'Origin']
  });

  // Compression
  await fastify.register(require('@fastify/compress'), {
    global: true,
    threshold: 1024,
    encodings: ['gzip', 'deflate']
  });

  // Serve Next.js static files
  await fastify.register(require('@fastify/static'), {
    root: path.join(__dirname, 'netstream-nextjs/.next/static'),
    prefix: '/_next/static/',
    decorateReply: false
  });

  // Serve Next.js public files
  await fastify.register(require('@fastify/static'), {
    root: path.join(__dirname, 'netstream-nextjs/public'),
    prefix: '/public/',
    decorateReply: false
  });

  // GraphQL endpoint
  console.log('📋 Loading GraphQL schema...');
  const schemaPath = path.join(__dirname, 'schema.graphql');
  console.log('📍 Schema path:', schemaPath);
  console.log('📁 Schema exists:', fs.existsSync(schemaPath));

  if (!fs.existsSync(schemaPath)) {
    throw new Error(`GraphQL schema file not found at: ${schemaPath}`);
  }

  const schema = fs.readFileSync(schemaPath, 'utf8');
  console.log('📋 Schema loaded, length:', schema.length);
  console.log('📋 Schema preview:', schema.substring(0, 200) + '...');

  console.log('🔧 Loading GraphQL resolvers...');
  const FastifyResolvers = require('./src/graphql/fastifyResolvers');
  const resolverInstance = new FastifyResolvers();
  const resolvers = resolverInstance.resolvers;
  console.log('🔧 Resolvers loaded, Query methods:', Object.keys(resolvers.Query || {}));

  console.log('🚀 Registering Mercurius GraphQL...');
  await fastify.register(require('mercurius'), {
    schema,
    resolvers,
    graphiql: true, // Always enable for debugging
    jit: 1,
    cache: false, // Disable cache for debugging
    queryDepth: 12,
    context: (request, reply) => ({
      db: fastify.db,
      cache: fastify.cacheService,
      logger: fastify.log
    })
  });
  console.log('✅ Mercurius GraphQL registered successfully');
}

// Register API routes
async function registerRoutes() {
  // Health check endpoint
  fastify.get('/health', async (request, reply) => {
    const startTime = Date.now();
    const health = {
      status: 'healthy',
      timestamp: new Date().toISOString(),
      service: 'netstream-combined',
      version: process.env.npm_package_version || '2.0.0',
      uptime: process.uptime(),
      memory: process.memoryUsage(),
      environment: process.env.NODE_ENV || 'development',
      render: !!process.env.RENDER,
      checks: {}
    };

    try {
      // Check database connection
      const dbStart = Date.now();
      await db.admin().ping();
      health.checks.database = {
        status: 'connected',
        responseTime: Date.now() - dbStart
      };
    } catch (error) {
      health.status = 'degraded';
      health.checks.database = {
        status: 'disconnected',
        error: error.message
      };
    }

    try {
      // Check cache connection
      if (cacheService) {
        const cacheStart = Date.now();
        const cacheHealth = await cacheService.healthCheck();
        health.checks.cache = {
          status: cacheHealth.status,
          responseTime: Date.now() - cacheStart,
          stats: cacheHealth.stats || {}
        };
      } else {
        health.checks.cache = { status: 'disabled' };
      }
    } catch (error) {
      health.status = 'degraded';
      health.checks.cache = {
        status: 'error',
        error: error.message
      };
    }

    // Add performance metrics
    health.performance = {
      totalResponseTime: Date.now() - startTime,
      memoryUsage: {
        rss: `${Math.round(health.memory.rss / 1024 / 1024)}MB`,
        heapTotal: `${Math.round(health.memory.heapTotal / 1024 / 1024)}MB`,
        heapUsed: `${Math.round(health.memory.heapUsed / 1024 / 1024)}MB`,
        external: `${Math.round(health.memory.external / 1024 / 1024)}MB`
      }
    };

    const statusCode = health.status === 'healthy' ? 200 : 503;
    return reply.code(statusCode).send(health);
  });

  // API info endpoint
  fastify.get('/api', async (request, reply) => {
    return {
      name: 'NetStream Combined API',
      version: '2.0.0',
      framework: 'Fastify + Next.js',
      graphql: '/graphql',
      health: '/health',
      timestamp: new Date().toISOString()
    };
  });

  // Frontend API health check
  fastify.get('/api/health', async (request, reply) => {
    return reply.redirect('/health');
  });

  // GraphQL test endpoint
  fastify.get('/api/graphql-test', async (request, reply) => {
    try {
      // Test a simple introspection query
      const result = await fastify.graphql(`
        query {
          __schema {
            queryType {
              name
              fields {
                name
              }
            }
          }
        }
      `);
      return {
        status: 'GraphQL Working',
        queryFields: result.data.__schema.queryType.fields.map(f => f.name)
      };
    } catch (error) {
      return {
        status: 'GraphQL Error',
        error: error.message
      };
    }
  });

  // Catch-all for Next.js pages (SPA routing)
  fastify.get('*', async (request, reply) => {
    // Skip API routes and static files
    if (request.url.startsWith('/graphql') ||
        request.url.startsWith('/api') ||
        request.url.startsWith('/_next') ||
        request.url.startsWith('/public')) {
      return reply.code(404).send({ error: 'Not found' });
    }

    // Try different Next.js build output locations
    const possiblePaths = [
      path.join(__dirname, 'netstream-nextjs/.next/standalone/server.js'),
      path.join(__dirname, 'netstream-nextjs/.next/server/app/page.html'),
      path.join(__dirname, 'netstream-nextjs/.next/server/pages/index.html'),
      path.join(__dirname, 'netstream-nextjs/.next/static/index.html'),
      path.join(__dirname, 'netstream-nextjs/out/index.html')
    ];

    console.log('🔍 Checking Next.js build paths...');
    for (const indexPath of possiblePaths) {
      console.log(`   ${fs.existsSync(indexPath) ? '✅' : '❌'} ${indexPath}`);
      if (fs.existsSync(indexPath) && indexPath.endsWith('.html')) {
        console.log(`📄 Serving Next.js from: ${indexPath}`);
        const html = fs.readFileSync(indexPath, 'utf8');
        return reply.type('text/html').send(html);
      }
    }

    // Check if standalone build exists and try to serve it
    const standalonePath = path.join(__dirname, 'netstream-nextjs/.next/standalone');
    if (fs.existsSync(standalonePath)) {
      console.log('📦 Next.js standalone build found, attempting to serve...');

      // Try to find the actual HTML files in standalone build
      const standaloneHtmlPaths = [
        path.join(standalonePath, 'index.html'),
        path.join(standalonePath, 'public/index.html'),
        path.join(__dirname, 'netstream-nextjs/.next/server/app/layout.html'),
        path.join(__dirname, 'netstream-nextjs/.next/server/app/page.html')
      ];

      for (const htmlPath of standaloneHtmlPaths) {
        if (fs.existsSync(htmlPath)) {
          console.log(`📄 Found Next.js HTML at: ${htmlPath}`);
          const html = fs.readFileSync(htmlPath, 'utf8');
          return reply.type('text/html').send(html);
        }
      }

      console.log('⚠️ Standalone build found but no HTML files, trying to generate...');

      // For Next.js App Router, we need to create a basic HTML shell that loads the app
      const nextAppHtml = `
        <!DOCTYPE html>
        <html lang="en">
          <head>
            <meta charset="utf-8" />
            <meta name="viewport" content="width=device-width, initial-scale=1" />
            <title>NetStream</title>
            <link rel="preload" href="/_next/static/css/app/layout.css" as="style" />
            <link rel="stylesheet" href="/_next/static/css/app/layout.css" />
          </head>
          <body>
            <div id="__next">
              <div style="padding: 20px; text-align: center; background: #000; color: #fff; min-height: 100vh;">
                <h1>🎬 NetStream</h1>
                <p>Loading Next.js App...</p>
                <div id="loading-status">Initializing...</div>
              </div>
            </div>
            <script>
              document.getElementById('loading-status').textContent = 'Loading application bundle...';
              // Try to load Next.js runtime
              const script = document.createElement('script');
              script.src = '/_next/static/chunks/webpack.js';
              script.onload = () => {
                document.getElementById('loading-status').textContent = 'Runtime loaded, starting app...';
              };
              script.onerror = () => {
                document.getElementById('loading-status').innerHTML =
                  '<p style="color: orange;">⚠️ Frontend build issue detected</p>' +
                  '<p>Backend is working. <a href="/graphql" style="color: #0070f3;">Test GraphQL</a></p>';
              };
              document.head.appendChild(script);
            </script>
          </body>
        </html>
      `;
      return reply.type('text/html').send(nextAppHtml);
    }

    // If no Next.js build found, serve a proper React app shell
    console.log('⚠️ Next.js build not found, serving React app shell');
    const reactAppHtml = `
      <!DOCTYPE html>
      <html lang="en">
        <head>
          <meta charset="utf-8" />
          <meta name="viewport" content="width=device-width, initial-scale=1" />
          <title>NetStream</title>
          <script src="https://unpkg.com/react@18/umd/react.production.min.js"></script>
          <script src="https://unpkg.com/react-dom@18/umd/react-dom.production.min.js"></script>
          <script src="https://unpkg.com/@apollo/client@3/dist/apollo-client.min.js"></script>
          <style>
            body { margin: 0; font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', sans-serif; background: #000; color: #fff; }
            .container { padding: 20px; text-align: center; }
            .loading { margin: 20px 0; }
            .links { margin: 20px 0; }
            .links a { color: #0070f3; margin: 0 10px; }
          </style>
        </head>
        <body>
          <div id="__next">
            <div class="container">
              <h1>🎬 NetStream</h1>
              <div class="loading">
                <p>Loading application...</p>
                <p>Connecting to GraphQL API...</p>
              </div>
              <div class="links">
                <a href="/graphql">GraphQL Playground</a>
                <a href="/health">Health Check</a>
                <a href="/api">API Info</a>
              </div>
              <script>
                // Simple GraphQL test
                fetch('/graphql', {
                  method: 'POST',
                  headers: { 'Content-Type': 'application/json' },
                  body: JSON.stringify({ query: '{ __schema { types { name } } }' })
                })
                .then(res => res.json())
                .then(data => {
                  if (data.data) {
                    document.querySelector('.loading').innerHTML = '<p style="color: green;">✅ GraphQL API Connected!</p><p>Backend is working. Frontend build needs to be fixed.</p>';
                  } else {
                    document.querySelector('.loading').innerHTML = '<p style="color: red;">❌ GraphQL API Error</p>';
                  }
                })
                .catch(err => {
                  document.querySelector('.loading').innerHTML = '<p style="color: red;">❌ Connection Error</p>';
                });
              </script>
            </div>
          </div>
        </body>
      </html>
    `;
    return reply.type('text/html').send(reactAppHtml);
  });
}

// Graceful shutdown
async function gracefulShutdown() {
  try {
    if (cacheService) {
      await cacheService.close();
      fastify.log.info('Cache service closed');
    }
    if (client) {
      await client.close();
      fastify.log.info('MongoDB connection closed');
    }
    await fastify.close();
    fastify.log.info('Server closed');
    process.exit(0);
  } catch (error) {
    fastify.log.error('Error during shutdown:', error);
    process.exit(1);
  }
}

process.on('SIGTERM', gracefulShutdown);
process.on('SIGINT', gracefulShutdown);

// Start server
async function start() {
  try {
    // Connect to database
    await connectToDatabase();
    
    // Initialize cache
    await initializeCacheService();
    
    // Register plugins and routes
    await registerPlugins();
    await registerRoutes();
    
    // Start listening
    await fastify.listen({ port: port, host: host });
    
    fastify.log.info(`🚀 NetStream Combined server listening on ${host}:${port}`);
    fastify.log.info(`📊 Health check: http://${host}:${port}/health`);
    fastify.log.info(`🔗 GraphQL endpoint: http://${host}:${port}/graphql`);
    fastify.log.info(`🎨 Frontend: http://${host}:${port}/`);
    
    if (process.env.RENDER) {
      fastify.log.info(`🌐 Render.com deployment detected`);
      fastify.log.info(`📦 Service: ${process.env.RENDER_SERVICE_NAME || 'netstream-combined'}`);
    }
  } catch (err) {
    console.error('❌ Server startup failed:', {
      message: err.message,
      code: err.code,
      name: err.name,
      env: {
        NODE_ENV: process.env.NODE_ENV,
        PORT: process.env.PORT,
        HOST: process.env.HOST,
        MONGO_URI: process.env.MONGO_URI ? 'SET' : 'NOT SET',
        RENDER: process.env.RENDER
      }
    });
    console.error('❌ Full startup error:', err);
    fastify.log.error('Error starting server:', err.message);
    process.exit(1);
  }
}

// Handle uncaught exceptions
process.on('uncaughtException', (error) => {
  fastify.log.error('Uncaught Exception:', error);
  gracefulShutdown();
});

process.on('unhandledRejection', (reason, promise) => {
  fastify.log.error('Unhandled Rejection at:', promise, 'reason:', reason);
  gracefulShutdown();
});

start();
