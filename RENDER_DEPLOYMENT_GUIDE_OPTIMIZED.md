# NetStream Render.com Deployment Guide - Optimized Version

## Overview

This guide covers deploying the **FULL-FEATURED** NetStream application to Render.com with all optimizations for cloud deployment while maintaining ALL existing functionality.

## What's Included

### ✅ All Original Features Maintained
- **Content Scraping**: Full Puppeteer-based scraping system
- **Video Streaming**: Complete video player with HLS support
- **Admin Panel**: Full admin functionality
- **Background Jobs**: Bull queues for scraping and processing
- **AI Enrichment**: Gemini AI integration
- **Live TV**: Live TV channels and streaming
- **User Management**: Authentication and profiles
- **Search**: Advanced search across all content types

### 🚀 Cloud Optimizations Added
- **Enhanced CORS**: Proper cross-service communication
- **Service Discovery**: Automatic URL configuration between services
- **Health Monitoring**: Comprehensive health checks
- **Performance Tuning**: Optimized for cloud deployment
- **Security**: Non-root containers and proper permissions
- **Logging**: Enhanced logging for cloud monitoring

## Architecture on Render.com

```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   Frontend      │    │    Backend      │    │     Redis       │
│   (Next.js)     │◄──►│   (Fastify)     │◄──►│   (Managed)     │
│   Port: 3000    │    │   Port: 3001    │    │   Port: 6379    │
└─────────────────┘    └─────────────────┘    └─────────────────┘
         │                       │                       │
         │                       │                       │
         ▼                       ▼                       ▼
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│  User Browser   │    │   MongoDB       │    │  External APIs  │
│                 │    │   (Atlas)       │    │  (TMDB, etc.)   │
└─────────────────┘    └─────────────────┘    └─────────────────┘
```

## Pre-Deployment Setup

### 1. Required Environment Variables

You'll need these values ready for Render.com dashboard:

```bash
# Database (Required)
MONGO_URI=mongodb+srv://your-connection-string

# API Keys (Required)
TMDB_API_KEY=your-tmdb-api-key

# API Keys (Optional but recommended)
GEMINI_API_KEY=your-gemini-api-key
TELEGRAM_TOKEN=your-telegram-bot-token
TELEGRAM_API_ID=your-telegram-api-id
TELEGRAM_API_HASH=your-telegram-api-hash
ONEUPLOAD_API_KEY=your-oneupload-key

# Authentication (Required)
JWT_SECRET=your-secure-jwt-secret

# Telegram Configuration (Optional)
PRIVATE_GROUP_ID=your-private-group-id
```

### 2. MongoDB Atlas Configuration

Ensure your MongoDB Atlas cluster:
- Allows connections from `0.0.0.0/0` (Render's dynamic IPs)
- Has proper indexes created (see deployment checklist)
- Connection string includes `retryWrites=true&w=majority`

## Deployment Steps

### Option 1: One-Click Deployment (Recommended)

1. **Fork/Clone Repository**
   - Ensure you're on the `render-deployment` branch
   - Push to your GitHub repository

2. **Deploy via Blueprint**
   - Go to [Render.com](https://render.com) and sign up
   - Click "New +" → "Blueprint"
   - Connect your GitHub repository
   - Select `render-deployment` branch
   - Render will read `render.yaml` automatically

3. **Set Environment Variables**
   - In the Render dashboard, set the required environment variables
   - The `render.yaml` file handles service connections automatically

### Option 2: Manual Service Creation

#### Step 1: Create Redis Service
```yaml
Service Type: Redis
Name: netstream-redis
Plan: Starter ($7/month)
Region: Oregon (US West)
Max Memory Policy: allkeys-lru
```

#### Step 2: Create Backend Service
```yaml
Service Type: Web Service
Name: netstream-backend
Environment: Docker
Dockerfile Path: ./Dockerfile
Plan: Starter ($7/month)
Region: Oregon (US West)
Build Command: (leave empty - Docker handles it)
Start Command: node server-fastify.js
Health Check Path: /health
```

**Environment Variables for Backend:**
```bash
NODE_ENV=production
FASTIFY_PORT=3001
HOST=0.0.0.0
RENDER=true
RENDER_SERVICE_NAME=netstream-backend

# Set these manually:
MONGO_URI=your-mongodb-connection-string
TMDB_API_KEY=your-tmdb-api-key
GEMINI_API_KEY=your-gemini-api-key
JWT_SECRET=your-jwt-secret

# These will be auto-set by Render:
REDIS_URL=(from Redis service)
FRONTEND_URL=(from Frontend service)
```

#### Step 3: Create Frontend Service
```yaml
Service Type: Web Service
Name: netstream-frontend
Environment: Docker
Dockerfile Path: ./netstream-nextjs/Dockerfile
Plan: Starter ($7/month)
Region: Oregon (US West)
Build Command: (leave empty - Docker handles it)
Start Command: node server.js
Health Check Path: /api/health
```

**Environment Variables for Frontend:**
```bash
NODE_ENV=production
PORT=3000
HOSTNAME=0.0.0.0
NEXT_TELEMETRY_DISABLED=1

# Set these manually:
JWT_SECRET=your-jwt-secret
MONGO_URI=your-mongodb-connection-string

# These will be auto-set by Render:
NEXT_PUBLIC_API_URL=(from Backend service)
NEXT_PUBLIC_API_BASE_URL=(from Backend service)
API_URL=(from Backend service)
```

## Post-Deployment Verification

### 1. Service Health Checks

**Backend Health:**
```bash
curl https://netstream-backend.onrender.com/health
```

Expected response:
```json
{
  "status": "healthy",
  "service": "netstream-backend",
  "uptime": 123.45,
  "checks": {
    "database": {"status": "connected", "responseTime": 15},
    "cache": {"status": "connected", "responseTime": 5}
  },
  "render": true
}
```

**Frontend Health:**
```bash
curl https://netstream-frontend.onrender.com/api/health
```

### 2. Functional Testing

1. **Visit Frontend**: `https://netstream-frontend.onrender.com`
2. **Test GraphQL**: `https://netstream-backend.onrender.com/graphql`
3. **Check Admin Panel**: Login and verify admin functionality
4. **Test Video Streaming**: Play a video to ensure streaming works
5. **Test Search**: Search for content across all categories
6. **Verify Scraping**: Check if background scraping jobs are running

### 3. Performance Monitoring

Monitor these metrics in Render dashboard:
- **Memory Usage**: Should be <80% of allocated
- **Response Times**: <500ms for API calls
- **Error Rates**: <1%
- **Uptime**: >99%

## Expected Performance

### Resource Usage
- **Backend**: 1GB RAM, 1 vCPU (Starter plan)
- **Frontend**: 512MB RAM, 0.5 vCPU (Starter plan)  
- **Redis**: 25MB memory (Starter plan)

### Response Times
- **API Responses**: 50-200ms
- **Page Load**: 1-3 seconds
- **Search**: 200-500ms
- **Video Streaming**: Immediate (direct links)

### Costs
- **Backend**: $7/month
- **Frontend**: $7/month
- **Redis**: $7/month
- **Total**: $21/month

## Troubleshooting

### Common Issues

#### 1. CORS Errors
```bash
# Check backend logs for CORS messages
# Verify FRONTEND_URL is set correctly
# Ensure origins are properly configured
```

#### 2. Service Communication Issues
```bash
# Verify environment variables are set:
echo $NEXT_PUBLIC_API_URL
echo $FRONTEND_URL
echo $REDIS_URL
```

#### 3. Database Connection Issues
```bash
# Check MongoDB Atlas IP whitelist
# Verify connection string format
# Test connection from backend logs
```

#### 4. Memory Issues
```bash
# Monitor memory usage in Render dashboard
# Check for memory leaks in application logs
# Consider upgrading to Standard plan if needed
```

### Performance Optimization

#### 1. Database Optimization
- Ensure all indexes are created
- Monitor slow queries
- Optimize aggregation pipelines

#### 2. Caching Optimization
- Monitor Redis hit rates
- Adjust cache TTL values
- Implement cache warming strategies

#### 3. Application Optimization
- Monitor response times
- Optimize GraphQL queries
- Implement query batching

## Scaling Considerations

### When to Scale Up

Consider upgrading plans when:
- Memory usage consistently >80%
- Response times >1 second
- Error rates >1%
- Need for more concurrent users

### Scaling Options

**Standard Plan ($25/month per service):**
- 2GB RAM, 1 vCPU
- Better performance
- More concurrent connections

**Pro Plan ($85/month per service):**
- 4GB RAM, 2 vCPU
- Dedicated resources
- Priority support

## Monitoring & Maintenance

### Daily Monitoring
- Check service status in Render dashboard
- Monitor error rates and response times
- Verify scraping jobs are running
- Check database and Redis connectivity

### Weekly Maintenance
- Review application logs
- Monitor resource usage trends
- Check for dependency updates
- Verify backup integrity

### Monthly Tasks
- Security updates
- Performance optimization review
- Cost analysis and optimization
- Capacity planning review

## Success Metrics

### Technical KPIs
- **Uptime**: >99.5%
- **Response Time**: <500ms average
- **Error Rate**: <0.5%
- **Memory Usage**: <80% of allocated

### Business KPIs
- **User Engagement**: Active users and session duration
- **Content Freshness**: Scraping success rates
- **Search Performance**: Search response times and relevance
- **Streaming Quality**: Video playback success rates

This optimized deployment maintains all NetStream features while providing excellent performance and reliability on Render.com's infrastructure.
