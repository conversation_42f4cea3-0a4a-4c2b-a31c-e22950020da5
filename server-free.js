// server-free.js - NetStream Free Tier Server
// Optimized for Render.com Free Tier (512MB RAM, 0.1 vCPU)

require('dotenv').config();
const fastify = require('fastify')({
  logger: {
    level: process.env.NODE_ENV === 'production' ? 'warn' : 'info',
    transport: process.env.NODE_ENV !== 'production' ? {
      target: 'pino-pretty',
      options: {
        colorize: true,
        translateTime: 'HH:MM:ss Z',
        ignore: 'pid,hostname'
      }
    } : undefined
  },
  trustProxy: true,
  bodyLimit: 1048576, // 1MB limit for free tier
  keepAliveTimeout: 5000, // Reduced for free tier
  connectionTimeout: 10000, // Reduced for free tier
  requestTimeout: 15000, // Reduced for free tier
  disableRequestLogging: true // Reduce memory usage
});

const path = require('path');
const { MongoClient } = require('mongodb');

// Import free tier configuration
const { mongoUri, port } = require('./src/config/env');

// Free tier cache (in-memory instead of Redis)
class FreeTierCache {
  constructor() {
    this.cache = new Map();
    this.maxSize = 200; // Very limited for free tier
    this.stats = { hits: 0, misses: 0, sets: 0 };
    this.cleanup();
  }

  set(key, value, ttl = 1800000) { // 30 minutes default
    if (this.cache.size >= this.maxSize) {
      this.evictOldest();
    }
    
    this.cache.set(key, {
      value,
      expires: Date.now() + ttl,
      created: Date.now()
    });
    this.stats.sets++;
  }

  get(key) {
    const item = this.cache.get(key);
    if (!item) {
      this.stats.misses++;
      return null;
    }
    
    if (Date.now() > item.expires) {
      this.cache.delete(key);
      this.stats.misses++;
      return null;
    }
    
    this.stats.hits++;
    return item.value;
  }

  evictOldest() {
    const firstKey = this.cache.keys().next().value;
    if (firstKey) {
      this.cache.delete(firstKey);
    }
  }

  cleanup() {
    setInterval(() => {
      const now = Date.now();
      for (const [key, item] of this.cache.entries()) {
        if (now > item.expires) {
          this.cache.delete(key);
        }
      }
    }, 300000); // Clean every 5 minutes
  }

  getStats() {
    return {
      ...this.stats,
      size: this.cache.size,
      hitRate: this.stats.hits + this.stats.misses > 0 
        ? Math.round((this.stats.hits / (this.stats.hits + this.stats.misses)) * 100)
        : 0
    };
  }
}

// Initialize cache
const cache = new FreeTierCache();

// Database connection
let db;
let client;

async function connectToDatabase() {
  try {
    client = new MongoClient(mongoUri, {
      maxPoolSize: 5, // Reduced for free tier
      minPoolSize: 1,
      maxIdleTimeMS: 30000,
      serverSelectionTimeoutMS: 5000,
      socketTimeoutMS: 45000,
      family: 4,
      keepAlive: true,
      keepAliveInitialDelay: 300000
    });

    await client.connect();
    db = client.db();
    fastify.log.info('Connected to MongoDB');
    
    // Add database to fastify context
    fastify.decorate('db', db);
    fastify.decorate('cache', cache);
    
    return db;
  } catch (error) {
    fastify.log.error('MongoDB connection error:', error);
    throw error;
  }
}

// Free tier GraphQL schema (minimal)
const minimalSchema = `
  type Query {
    movies(page: Int = 1, limit: Int = 10): [Movie!]!
    series(page: Int = 1, limit: Int = 10): [Series!]!
    anime(page: Int = 1, limit: Int = 10): [Anime!]!
    search(query: String!, limit: Int = 5): [SearchResult!]!
    health: HealthStatus!
  }

  type Movie {
    id: ID!
    title: String!
    year: Int
    poster: String
    genre: [String!]
    rating: Float
  }

  type Series {
    id: ID!
    title: String!
    year: Int
    poster: String
    genre: [String!]
    seasons: Int
  }

  type Anime {
    id: ID!
    title: String!
    year: Int
    poster: String
    genre: [String!]
    episodes: Int
  }

  type SearchResult {
    id: ID!
    title: String!
    type: String!
    poster: String
    year: Int
  }

  type HealthStatus {
    status: String!
    memory: MemoryUsage!
    cache: CacheStats!
    uptime: Float!
  }

  type MemoryUsage {
    rss: String!
    heapTotal: String!
    heapUsed: String!
  }

  type CacheStats {
    size: Int!
    hits: Int!
    misses: Int!
    hitRate: Int!
  }
`;

// Free tier resolvers (minimal functionality)
const minimalResolvers = {
  Query: {
    movies: async (parent, args, context) => {
      const { page = 1, limit = 10 } = args;
      const cacheKey = `movies:${page}:${limit}`;
      
      // Check cache first
      const cached = cache.get(cacheKey);
      if (cached) return cached;
      
      try {
        const movies = await db.collection('movies')
          .find({}, { 
            projection: { 
              title: 1, 
              year: 1, 
              poster: 1, 
              'tmdb.genres': 1,
              'tmdb.vote_average': 1
            }
          })
          .sort({ updatedAt: -1 })
          .skip((page - 1) * limit)
          .limit(limit)
          .toArray();

        const result = movies.map(movie => ({
          id: movie._id.toString(),
          title: movie.title,
          year: movie.year,
          poster: movie.poster,
          genre: movie.tmdb?.genres || [],
          rating: movie.tmdb?.vote_average || 0
        }));

        // Cache for 30 minutes
        cache.set(cacheKey, result, 1800000);
        return result;
      } catch (error) {
        fastify.log.error('Error fetching movies:', error);
        return [];
      }
    },

    series: async (parent, args, context) => {
      const { page = 1, limit = 10 } = args;
      const cacheKey = `series:${page}:${limit}`;
      
      const cached = cache.get(cacheKey);
      if (cached) return cached;
      
      try {
        const series = await db.collection('series')
          .find({}, { 
            projection: { 
              title: 1, 
              year: 1, 
              poster: 1, 
              'tmdb.genres': 1,
              'tmdb.number_of_seasons': 1
            }
          })
          .sort({ updatedAt: -1 })
          .skip((page - 1) * limit)
          .limit(limit)
          .toArray();

        const result = series.map(show => ({
          id: show._id.toString(),
          title: show.title,
          year: show.year,
          poster: show.poster,
          genre: show.tmdb?.genres || [],
          seasons: show.tmdb?.number_of_seasons || 0
        }));

        cache.set(cacheKey, result, 1800000);
        return result;
      } catch (error) {
        fastify.log.error('Error fetching series:', error);
        return [];
      }
    },

    anime: async (parent, args, context) => {
      const { page = 1, limit = 10 } = args;
      const cacheKey = `anime:${page}:${limit}`;
      
      const cached = cache.get(cacheKey);
      if (cached) return cached;
      
      try {
        const anime = await db.collection('animes')
          .find({}, { 
            projection: { 
              title: 1, 
              year: 1, 
              poster: 1, 
              'jikan.genres': 1,
              'jikan.episodes': 1
            }
          })
          .sort({ updatedAt: -1 })
          .skip((page - 1) * limit)
          .limit(limit)
          .toArray();

        const result = anime.map(item => ({
          id: item._id.toString(),
          title: item.title,
          year: item.year,
          poster: item.poster,
          genre: item.jikan?.genres?.map(g => g.name) || [],
          episodes: item.jikan?.episodes || 0
        }));

        cache.set(cacheKey, result, 1800000);
        return result;
      } catch (error) {
        fastify.log.error('Error fetching anime:', error);
        return [];
      }
    },

    search: async (parent, args, context) => {
      const { query, limit = 5 } = args;
      const cacheKey = `search:${query}:${limit}`;
      
      const cached = cache.get(cacheKey);
      if (cached) return cached;
      
      try {
        const searchResults = [];
        
        // Search movies
        const movies = await db.collection('movies')
          .find(
            { $text: { $search: query } },
            { projection: { title: 1, year: 1, poster: 1 } }
          )
          .limit(Math.ceil(limit / 3))
          .toArray();
        
        movies.forEach(movie => {
          searchResults.push({
            id: movie._id.toString(),
            title: movie.title,
            type: 'MOVIE',
            poster: movie.poster,
            year: movie.year
          });
        });

        // Search series
        const series = await db.collection('series')
          .find(
            { $text: { $search: query } },
            { projection: { title: 1, year: 1, poster: 1 } }
          )
          .limit(Math.ceil(limit / 3))
          .toArray();
        
        series.forEach(show => {
          searchResults.push({
            id: show._id.toString(),
            title: show.title,
            type: 'SERIES',
            poster: show.poster,
            year: show.year
          });
        });

        const result = searchResults.slice(0, limit);
        cache.set(cacheKey, result, 900000); // 15 minutes for search
        return result;
      } catch (error) {
        fastify.log.error('Error searching:', error);
        return [];
      }
    },

    health: async () => {
      const memoryUsage = process.memoryUsage();
      return {
        status: 'ok',
        memory: {
          rss: `${Math.round(memoryUsage.rss / 1024 / 1024)}MB`,
          heapTotal: `${Math.round(memoryUsage.heapTotal / 1024 / 1024)}MB`,
          heapUsed: `${Math.round(memoryUsage.heapUsed / 1024 / 1024)}MB`
        },
        cache: cache.getStats(),
        uptime: process.uptime()
      };
    }
  }
};

// Register plugins
async function registerPlugins() {
  // CORS
  await fastify.register(require('@fastify/cors'), {
    origin: true,
    credentials: true
  });

  // Serve static files (frontend)
  await fastify.register(require('@fastify/static'), {
    root: path.join(__dirname, 'public'),
    prefix: '/',
    decorateReply: false
  });

  // GraphQL
  await fastify.register(require('mercurius'), {
    schema: minimalSchema,
    resolvers: minimalResolvers,
    graphiql: false, // Disabled for production
    context: (request, reply) => ({
      db: fastify.db,
      cache: fastify.cache
    })
  });
}

// Routes
async function registerRoutes() {
  // Health check endpoint
  fastify.get('/health', async (request, reply) => {
    const memoryUsage = process.memoryUsage();
    return {
      status: 'ok',
      timestamp: new Date().toISOString(),
      uptime: process.uptime(),
      memory: {
        rss: `${Math.round(memoryUsage.rss / 1024 / 1024)}MB`,
        heapTotal: `${Math.round(memoryUsage.heapTotal / 1024 / 1024)}MB`,
        heapUsed: `${Math.round(memoryUsage.heapUsed / 1024 / 1024)}MB`
      },
      cache: cache.getStats(),
      database: db ? 'connected' : 'disconnected'
    };
  });

  // API routes prefix
  fastify.register(async function (fastify) {
    // All API routes are handled by GraphQL
    fastify.get('/api/health', async (request, reply) => {
      return reply.redirect('/health');
    });
  }, { prefix: '/api' });

  // Catch-all for frontend routes (SPA)
  fastify.get('*', async (request, reply) => {
    return reply.sendFile('index.html');
  });
}

// Graceful shutdown
async function gracefulShutdown() {
  try {
    if (client) {
      await client.close();
      fastify.log.info('MongoDB connection closed');
    }
    await fastify.close();
    fastify.log.info('Server closed');
    process.exit(0);
  } catch (error) {
    fastify.log.error('Error during shutdown:', error);
    process.exit(1);
  }
}

process.on('SIGTERM', gracefulShutdown);
process.on('SIGINT', gracefulShutdown);

// Start server
async function start() {
  try {
    // Connect to database
    await connectToDatabase();
    
    // Register plugins and routes
    await registerPlugins();
    await registerRoutes();
    
    // Start listening
    const PORT = process.env.PORT || 3000;
    await fastify.listen({ port: PORT, host: '0.0.0.0' });
    
    fastify.log.info(`NetStream Free Tier server running on port ${PORT}`);
    fastify.log.info(`Memory usage: ${Math.round(process.memoryUsage().heapUsed / 1024 / 1024)}MB`);
  } catch (err) {
    fastify.log.error('Error starting server:', err);
    process.exit(1);
  }
}

// Handle uncaught exceptions
process.on('uncaughtException', (error) => {
  fastify.log.error('Uncaught Exception:', error);
  gracefulShutdown();
});

process.on('unhandledRejection', (reason, promise) => {
  fastify.log.error('Unhandled Rejection at:', promise, 'reason:', reason);
  gracefulShutdown();
});

start();
