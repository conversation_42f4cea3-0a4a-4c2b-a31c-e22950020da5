services:
  # Redis Service - Managed Redis for caching
  - type: redis
    name: netstream-redis
    plan: starter
    maxmemoryPolicy: allkeys-lru
    region: oregon

  # Backend Service - Fastify + GraphQL + Workers
  - type: web
    name: netstream-backend
    env: docker
    dockerfilePath: ./Dockerfile
    plan: starter
    region: oregon
    buildCommand: echo "Building with Docker"
    startCommand: node server-fastify.js
    healthCheckPath: /health
    autoDeploy: true
    
    envVars:
      # Application Configuration
      - key: NODE_ENV
        value: production
      
      - key: FASTIFY_PORT
        value: 3001
      
      - key: HOST
        value: 0.0.0.0
      
      # Render.com Detection
      - key: RENDER
        value: true
      
      - key: RENDER_SERVICE_NAME
        value: netstream-backend
      
      # Frontend URL (automatically set by Ren<PERSON>)
      - key: FRONTEND_URL
        fromService:
          type: web
          name: netstream-frontend
          property: host
      
      # Redis Connection (automatically set by Ren<PERSON>)
      - key: REDIS_URL
        fromService:
          type: redis
          name: netstream-redis
          property: connectionString
      
      # Database Configuration (set these manually in Render dashboard)
      - key: MONGO_URI
        sync: false
      
      # API Keys (set these manually in Render dashboard)
      - key: TMDB_API_KEY
        sync: false
      
      - key: GEMINI_API_KEY
        sync: false
      
      - key: TELEGRAM_TOKEN
        sync: false
      
      - key: TELEGRAM_API_ID
        sync: false
      
      - key: TELEGRAM_API_HASH
        sync: false
      
      # Application Settings
      - key: SCRAPE_MODE
        value: latest
      
      - key: MAX_CONCURRENT_PAGES
        value: 3
      
      - key: ENABLE_CACHING
        value: true
      
      - key: USE_ADVANCED_ENRICHMENT
        value: true
      
      - key: FETCH_SEASONS
        value: true
      
      # Performance Settings
      - key: MAX_RETRY_ATTEMPTS
        value: 3
      
      - key: RETRY_DELAY_BASE
        value: 3000
      
      - key: MAX_CONCURRENT_PUPPETEER_TASKS
        value: 2
      
      - key: MAX_CONCURRENT_AXIOS_TASKS
        value: 3
      
      # Rate Limits
      - key: GEMINI_RATE_LIMIT
        value: 28
      
      - key: TMDB_RATE_LIMIT
        value: 40
      
      - key: JIKAN_RATE_LIMIT
        value: 50
      
      # Telegram Configuration
      - key: PRIVATE_GROUP_ID
        sync: false
      
      - key: WIFLIX_CHANNEL
        value: "@wiflix_updates"
      
      # External APIs
      - key: ONEUPLOAD_API_KEY
        sync: false

  # Frontend Service - Next.js
  - type: web
    name: netstream-frontend
    env: docker
    dockerfilePath: ./netstream-nextjs/Dockerfile
    plan: starter
    region: oregon
    buildCommand: echo "Building with Docker"
    startCommand: node server.js
    healthCheckPath: /api/health
    autoDeploy: true
    
    envVars:
      # Application Configuration
      - key: NODE_ENV
        value: production
      
      - key: PORT
        value: 3000
      
      - key: HOSTNAME
        value: 0.0.0.0
      
      # Disable Next.js telemetry
      - key: NEXT_TELEMETRY_DISABLED
        value: 1
      
      # Backend API URL (automatically set by Render)
      - key: NEXT_PUBLIC_API_URL
        fromService:
          type: web
          name: netstream-backend
          property: host
          envVarKey: GRAPHQL_ENDPOINT
      
      - key: NEXT_PUBLIC_API_BASE_URL
        fromService:
          type: web
          name: netstream-backend
          property: host
      
      - key: NEXT_PUBLIC_BACKEND_URL
        fromService:
          type: web
          name: netstream-backend
          property: host
      
      # Internal API URL for server-side requests
      - key: API_URL
        fromService:
          type: web
          name: netstream-backend
          property: host
          envVarKey: GRAPHQL_ENDPOINT
      
      # Authentication (same as backend)
      - key: JWT_SECRET
        sync: false
      
      # Database (for build-time operations)
      - key: MONGO_URI
        sync: false

# Optional: Database (if you want to use Render's managed PostgreSQL instead of MongoDB Atlas)
# databases:
#   - name: netstream-db
#     databaseName: netstream
#     user: netstream
#     plan: starter
