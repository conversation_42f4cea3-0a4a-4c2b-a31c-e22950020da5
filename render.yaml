# Render.com deployment configuration for NetStream Free Tier
services:
  - type: web
    name: netstream-free-tier
    env: docker
    dockerfilePath: ./Dockerfile.free
    plan: free
    region: oregon
    branch: render-deployment
    buildCommand: echo "Building with Docker"
    startCommand: node server-free.js
    healthCheckPath: /health
    autoDeploy: true
    
    envVars:
      - key: NODE_ENV
        value: production
      
      - key: PORT
        value: 3000
      
      - key: RENDER_FREE_TIER
        value: true
      
      - key: MONGO_URI
        sync: false
        # Set this in Render dashboard
      
      - key: TMDB_API_KEY
        sync: false
        # Set this in Render dashboard
      
      - key: ENABLE_SCRAPING
        value: false
      
      - key: ENABLE_BACKGROUND_JOBS
        value: false
      
      - key: ENABLE_ADVANCED_ENRICHMENT
        value: false
      
      - key: ENABLE_PUPPETEER
        value: false
      
      - key: ENABLE_REDIS
        value: false
      
      - key: ENABLE_VIDEO_STREAMING
        value: false
      
      - key: ENABLE_ADMIN_PANEL
        value: false
      
      - key: MAX_CONCURRENT_REQUESTS
        value: 5
      
      - key: MAX_ITEMS_PER_PAGE
        value: 10
      
      - key: MAX_SEARCH_RESULTS
        value: 5
      
      - key: MAX_CACHE_SIZE
        value: 200
      
      - key: CACHE_TTL
        value: 1800000
      
      - key: NODE_OPTIONS
        value: --max-old-space-size=400
      
      - key: LOG_LEVEL
        value: warn
      
      - key: DISABLE_REQUEST_LOGGING
        value: true
      
      - key: DB_MAX_POOL_SIZE
        value: 5
      
      - key: DB_MIN_POOL_SIZE
        value: 1
      
      - key: DB_CONNECTION_TIMEOUT
        value: 5000
      
      - key: CACHE_STRATEGY
        value: minimal
      
      - key: ENABLE_QUERY_CACHE
        value: true
      
      - key: ENABLE_RESULT_CACHE
        value: true
      
      - key: CORS_ORIGIN
        value: "*"
      
      - key: TRUST_PROXY
        value: true
      
      - key: HEALTH_CHECK_INTERVAL
        value: 30000
      
      - key: RENDER
        value: true
