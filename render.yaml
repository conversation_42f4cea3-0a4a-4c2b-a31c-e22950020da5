# Option 1: Combined Service (Frontend + Backend) - $14/month total
services:
  # Redis Service - Managed Redis for caching
  - type: redis
    name: netstream-redis
    plan: starter
    maxmemoryPolicy: allkeys-lru
    region: oregon

  # Combined Service - Frontend + Backend in one container
  - type: web
    name: netstream-combined
    env: docker
    dockerfilePath: ./Dockerfile.combined
    plan: starter
    region: oregon
    buildCommand: echo "Building combined service with Docker"
    startCommand: node server-combined.js
    healthCheckPath: /health
    autoDeploy: true

    envVars:
      # Application Configuration
      - key: NODE_ENV
        value: production

      - key: PORT
        value: 3001

      - key: HOST
        value: 0.0.0.0

      # Render.com Detection
      - key: RENDER
        value: true

      - key: RENDER_SERVICE_NAME
        value: netstream-combined

      # Redis Connection (automatically set by Render)
      - key: REDIS_URL
        fromService:
          type: redis
          name: netstream-redis
          property: connectionString
      
      # Database Configuration (set these manually in Render dashboard)
      - key: MONGO_URI
        sync: false
      
      # API Keys (set these manually in Render dashboard)
      - key: TMDB_API_KEY
        sync: false
      
      - key: GEMINI_API_KEY
        sync: false
      
      - key: TELEGRAM_TOKEN
        sync: false
      
      - key: TELEGRAM_API_ID
        sync: false
      
      - key: TELEGRAM_API_HASH
        sync: false
      
      # Application Settings
      - key: SCRAPE_MODE
        value: latest
      
      - key: MAX_CONCURRENT_PAGES
        value: 3
      
      - key: ENABLE_CACHING
        value: true
      
      - key: USE_ADVANCED_ENRICHMENT
        value: true
      
      - key: FETCH_SEASONS
        value: true
      
      # Performance Settings
      - key: MAX_RETRY_ATTEMPTS
        value: 3
      
      - key: RETRY_DELAY_BASE
        value: 3000
      
      - key: MAX_CONCURRENT_PUPPETEER_TASKS
        value: 2
      
      - key: MAX_CONCURRENT_AXIOS_TASKS
        value: 3
      
      # Rate Limits
      - key: GEMINI_RATE_LIMIT
        value: 28
      
      - key: TMDB_RATE_LIMIT
        value: 40
      
      - key: JIKAN_RATE_LIMIT
        value: 50
      
      # Telegram Configuration
      - key: PRIVATE_GROUP_ID
        sync: false
      
      - key: WIFLIX_CHANNEL
        value: "@wiflix_updates"
      
      # External APIs
      - key: ONEUPLOAD_API_KEY
        sync: false

# Note: Frontend is now served by the combined service above
# No separate frontend service needed

# Optional: Database (if you want to use Render's managed PostgreSQL instead of MongoDB Atlas)
# databases:
#   - name: netstream-db
#     databaseName: netstream
#     user: netstream
#     plan: starter
