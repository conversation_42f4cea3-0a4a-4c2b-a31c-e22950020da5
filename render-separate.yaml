# Option 2: Separate Services (Frontend + Backend) - $21/month total
services:
  # Redis Service - Managed Redis for caching
  - type: redis
    name: netstream-redis
    plan: starter
    maxmemoryPolicy: allkeys-lru
    region: oregon

  # Backend Service - Fastify + GraphQL + Workers
  - type: web
    name: netstream-backend
    env: docker
    dockerfilePath: ./Dockerfile
    plan: starter
    region: oregon
    buildCommand: echo "Building with Docker"
    startCommand: node server-fastify.js
    healthCheckPath: /health
    autoDeploy: true
    
    envVars:
      # Application Configuration
      - key: NODE_ENV
        value: production
      
      - key: FASTIFY_PORT
        value: 3001
      
      - key: HOST
        value: 0.0.0.0
      
      # Render.com Detection
      - key: RENDER
        value: true
      
      - key: RENDER_SERVICE_NAME
        value: netstream-backend
      
      # Frontend URL (automatically set by Ren<PERSON>)
      - key: FRONTEND_URL
        fromService:
          type: web
          name: netstream-frontend
          property: host
      
      # Redis Connection (automatically set by <PERSON><PERSON>)
      - key: REDIS_URL
        fromService:
          type: redis
          name: netstream-redis
          property: connectionString
      
      # Database Configuration (set these manually in Render dashboard)
      - key: MONGO_URI
        sync: false
      
      # API Keys (set these manually in Render dashboard)
      - key: TMDB_API_KEY
        sync: false
      
      - key: GEMINI_API_KEY
        sync: false
      
      - key: TELEGRAM_TOKEN
        sync: false
      
      - key: JWT_SECRET
        sync: false
      
      # Application Settings
      - key: SCRAPE_MODE
        value: latest
      
      - key: MAX_CONCURRENT_PAGES
        value: 3
      
      - key: ENABLE_CACHING
        value: true

  # Frontend Service - Next.js
  - type: web
    name: netstream-frontend
    env: docker
    dockerfilePath: ./netstream-nextjs/Dockerfile
    plan: starter
    region: oregon
    buildCommand: echo "Building with Docker"
    startCommand: node server.js
    healthCheckPath: /api/health
    autoDeploy: true
    
    envVars:
      # Application Configuration
      - key: NODE_ENV
        value: production
      
      - key: PORT
        value: 3000
      
      - key: HOSTNAME
        value: 0.0.0.0
      
      # Disable Next.js telemetry
      - key: NEXT_TELEMETRY_DISABLED
        value: 1
      
      # Backend API URL (automatically set by Render)
      - key: NEXT_PUBLIC_API_URL
        fromService:
          type: web
          name: netstream-backend
          property: host
          envVarKey: GRAPHQL_ENDPOINT
      
      - key: NEXT_PUBLIC_API_BASE_URL
        fromService:
          type: web
          name: netstream-backend
          property: host
      
      # Authentication (same as backend)
      - key: JWT_SECRET
        sync: false
      
      # Database (for build-time operations)
      - key: MONGO_URI
        sync: false
