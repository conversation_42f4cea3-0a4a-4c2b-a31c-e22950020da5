#!/usr/bin/env node

/**
 * Test script for Gemini AI functionality
 * This script tests the Gemini API key from the database and verifies enrichment works
 */

const { MongoClient } = require('mongodb');
const { GoogleGenerativeAI } = require('@google/generative-ai');
require('dotenv').config();

const GEMINI_MODEL_NAME = "gemini-2.0-flash-lite";

// Function to get Gemini API key from database
async function getGeminiApiKey() {
  try {
    console.log('🔍 Connecting to MongoDB to get Gemini API key...');
    const uri = process.env.MONGO_URI;
    if (!uri) {
      throw new Error('MONGO_URI not found in environment variables');
    }
    
    const client = new MongoClient(uri);
    await client.connect();
    console.log('✅ Connected to MongoDB successfully');
    
    const db = client.db('NetStream');
    const config = await db.collection('config').findOne({ key: 'GEMINI_API_KEY' });
    await client.close();
    console.log('🔐 Database connection closed');

    if (config && config.value) {
      console.log('✅ Found Gemini API key in database');
      return config.value;
    } else {
      console.log('❌ No Gemini API key found in database config collection');
      return null;
    }
  } catch (error) {
    console.error('❌ Failed to get Gemini API key from database:', error.message);
    return null;
  }
}

// Function to test Gemini API
async function testGeminiAPI(apiKey) {
  try {
    console.log('🤖 Initializing Gemini AI...');
    const genAI = new GoogleGenerativeAI(apiKey);
    const model = genAI.getGenerativeModel({ model: GEMINI_MODEL_NAME });
    
    console.log('📝 Testing Gemini with a simple prompt...');
    const prompt = `Given the following basic information for a movie:
Original Title: "The Dark Knight"
Approximate Release Year: 2008

Please provide the most accurate and complete title for searching on TMDB (The Movie Database). Consider:
- Original language title variations
- International release titles
- Common alternative titles
- Year disambiguation if needed

Respond with ONLY the best search title, nothing else.`;

    const result = await model.generateContent(prompt);
    const response = await result.response;
    const text = response.text();
    
    console.log('✅ Gemini API test successful!');
    console.log('📤 Prompt:', prompt.substring(0, 100) + '...');
    console.log('📥 Response:', text);
    
    return true;
  } catch (error) {
    console.error('❌ Gemini API test failed:', error.message);
    if (error.message.includes('quota')) {
      console.error('💸 Quota exceeded - the API key has reached its usage limit');
    } else if (error.message.includes('API_KEY_INVALID')) {
      console.error('🔑 Invalid API key');
    } else if (error.message.includes('permission')) {
      console.error('🚫 Permission denied - check API key permissions');
    }
    return false;
  }
}

// Function to test enrichment service
async function testEnrichmentService() {
  try {
    console.log('🔧 Testing enrichment service...');
    
    // Import the enrichment service
    const { generateSearchQuery } = require('./src/enrichment/services/advancedEnrichService');
    
    const testTitle = "The Dark Knight";
    const testYear = 2008;
    const mediaType = "movie";
    
    console.log(`📝 Testing enrichment for: "${testTitle}" (${testYear})`);
    
    const enrichedTitle = await generateSearchQuery(testTitle, mediaType, testYear);
    
    console.log('✅ Enrichment service test successful!');
    console.log('📤 Original title:', testTitle);
    console.log('📥 Enriched title:', enrichedTitle);
    
    return true;
  } catch (error) {
    console.error('❌ Enrichment service test failed:', error.message);
    return false;
  }
}

// Main test function
async function runGeminiTests() {
  console.log('🚀 Starting Gemini AI tests...\n');
  
  try {
    // Test 1: Get API key from database
    console.log('=== Test 1: Database API Key Retrieval ===');
    const apiKey = await getGeminiApiKey();
    
    if (!apiKey) {
      console.log('❌ Cannot proceed without API key');
      process.exit(1);
    }
    
    console.log('✅ API key retrieved successfully\n');
    
    // Test 2: Direct Gemini API test
    console.log('=== Test 2: Direct Gemini API Test ===');
    const apiTest = await testGeminiAPI(apiKey);
    
    if (!apiTest) {
      console.log('❌ Direct API test failed');
      process.exit(1);
    }
    
    console.log('✅ Direct API test passed\n');
    
    // Test 3: Enrichment service test
    console.log('=== Test 3: Enrichment Service Test ===');
    const enrichmentTest = await testEnrichmentService();
    
    if (!enrichmentTest) {
      console.log('❌ Enrichment service test failed');
      process.exit(1);
    }
    
    console.log('✅ Enrichment service test passed\n');
    
    console.log('🎉 All Gemini tests passed successfully!');
    console.log('✅ Gemini enrichment is working properly with the database API key');
    
  } catch (error) {
    console.error('💥 Test suite failed:', error.message);
    process.exit(1);
  }
}

// Run the tests
if (require.main === module) {
  runGeminiTests().catch(error => {
    console.error('💥 Unexpected error:', error);
    process.exit(1);
  });
}

module.exports = {
  getGeminiApiKey,
  testGeminiAPI,
  testEnrichmentService,
  runGeminiTests
};
