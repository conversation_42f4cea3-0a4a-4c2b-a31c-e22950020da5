version: '3.8'

services:
  redis:
    image: redis:7-alpine
    container_name: netstream-redis
    ports:
      - "6379:6379"
    volumes:
      - redis_data:/data
    command: redis-server --appendonly yes
    restart: unless-stopped
    healthcheck:
      test: ["C<PERSON>", "redis-cli", "ping"]
      interval: 10s
      timeout: 3s
      retries: 3
    networks:
      - netstream-network

  backend:
    build: .
    container_name: netstream-backend-container
    ports:
      - "3001:3001"
    environment:
      - REDIS_URL=redis://redis:6379
    env_file:
      - .env
    depends_on:
      redis:
        condition: service_healthy
    restart: unless-stopped
    volumes:
      - ./logs:/app/logs
    networks:
      - netstream-network

  frontend:
    build:
      context: .
      dockerfile: Dockerfile.frontend
    container_name: netstream-frontend-container
    ports:
      - "3000:3000"
    environment:
      - API_URL=http://backend:3001/graphql
      - NEXT_PUBLIC_API_URL=http://localhost:3001/graphql
      - NEXT_PUBLIC_GRAPHQL_ENDPOINT=http://localhost:3001/graphql
      - NEXT_PUBLIC_API_BASE_URL=http://localhost:3001
    depends_on:
      - backend
    restart: unless-stopped
    networks:
      - netstream-network

volumes:
  redis_data:

networks:
  netstream-network:
    driver: bridge
