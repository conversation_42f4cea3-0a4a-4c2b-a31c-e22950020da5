#!/usr/bin/env node

/**
 * Test script for Live TV streaming functionality
 * This script tests Live TV stream URLs to verify they are accessible
 */

const axios = require('axios');
require('dotenv').config();

async function testLiveTVStreams() {
  console.log('🚀 Starting Live TV stream tests...\n');
  
  try {
    // First, get Live TV channels from GraphQL
    console.log('=== Test 1: Fetching Live TV Channels ===');
    const graphqlQuery = {
      query: `
        query {
          liveTV(limit: 5, page: 1) {
            id
            title
            image
            streamingUrls {
              id
              url
              provider
            }
          }
        }
      `
    };
    
    const response = await axios.post('http://localhost:3001/graphql', graphqlQuery, {
      headers: { 'Content-Type': 'application/json' }
    });
    
    if (!response.data?.data?.liveTV) {
      throw new Error('No Live TV data received from GraphQL');
    }
    
    const channels = response.data.data.liveTV;
    console.log(`✅ Found ${channels.length} Live TV channels`);
    
    // Test each channel's streaming URLs
    console.log('\n=== Test 2: Testing Stream URLs ===');
    for (const channel of channels) {
      console.log(`\n📺 Testing channel: ${channel.title}`);
      
      if (!channel.streamingUrls || channel.streamingUrls.length === 0) {
        console.log('❌ No streaming URLs found for this channel');
        continue;
      }
      
      for (const streamUrl of channel.streamingUrls) {
        console.log(`🔗 Testing URL: ${streamUrl.url}`);
        console.log(`📡 Provider: ${streamUrl.provider}`);
        
        try {
          // Test if the stream URL is accessible
          const streamResponse = await axios.head(streamUrl.url, {
            timeout: 10000,
            headers: {
              'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36'
            }
          });
          
          console.log(`✅ Stream accessible - Status: ${streamResponse.status}`);
          console.log(`📄 Content-Type: ${streamResponse.headers['content-type'] || 'Unknown'}`);
          
          // Check if it's an HLS stream
          if (streamUrl.url.includes('.m3u8')) {
            console.log('🎬 Detected HLS stream (.m3u8)');
            
            // Try to fetch the m3u8 content
            try {
              const m3u8Response = await axios.get(streamUrl.url, {
                timeout: 10000,
                headers: {
                  'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36'
                }
              });
              
              const m3u8Content = m3u8Response.data;
              if (m3u8Content.includes('#EXTM3U')) {
                console.log('✅ Valid HLS playlist detected');
                
                // Count the number of segments
                const segments = m3u8Content.split('\n').filter(line => 
                  line.trim() && !line.startsWith('#')
                ).length;
                console.log(`📊 Found ${segments} stream segments/variants`);
              } else {
                console.log('❌ Invalid HLS playlist format');
              }
            } catch (m3u8Error) {
              console.log(`❌ Failed to fetch HLS playlist: ${m3u8Error.message}`);
            }
          }
          
        } catch (error) {
          console.log(`❌ Stream not accessible: ${error.message}`);
          if (error.response) {
            console.log(`   Status: ${error.response.status} ${error.response.statusText}`);
          }
        }
      }
    }
    
    // Test the GraphQL stream resolver
    console.log('\n=== Test 3: Testing GraphQL Stream Resolver ===');
    if (channels.length > 0 && channels[0].streamingUrls.length > 0) {
      const testChannel = channels[0];
      const testStream = testChannel.streamingUrls[0];
      
      console.log(`🧪 Testing stream resolver for: ${testChannel.title}`);
      
      const streamQuery = {
        query: `
          query {
            stream(itemId: "${testChannel.id}", type: LIVETV, streamId: "${testStream.id}") {
              sourceStreamUrl
              size
              type
              method
            }
          }
        `
      };
      
      try {
        const streamResponse = await axios.post('http://localhost:3001/graphql', streamQuery, {
          headers: { 'Content-Type': 'application/json' }
        });
        
        const streamData = streamResponse.data?.data?.stream;
        if (streamData) {
          console.log('✅ Stream resolver responded');
          console.log(`📤 Source Stream URL: ${streamData.sourceStreamUrl || 'null'}`);
          console.log(`📏 Size: ${streamData.size || 'null'}`);
          console.log(`🎭 Type: ${streamData.type || 'null'}`);
          console.log(`🔧 Method: ${streamData.method || 'null'}`);
          
          if (!streamData.sourceStreamUrl) {
            console.log('ℹ️  No source stream URL - will use original URL');
          }
        } else {
          console.log('❌ No stream data received from resolver');
        }
      } catch (streamError) {
        console.log(`❌ Stream resolver error: ${streamError.message}`);
      }
    }
    
    console.log('\n🎉 Live TV stream tests completed!');
    
  } catch (error) {
    console.error('💥 Test suite failed:', error.message);
    process.exit(1);
  }
}

// Run the tests
if (require.main === module) {
  testLiveTVStreams().catch(error => {
    console.error('💥 Unexpected error:', error);
    process.exit(1);
  });
}

module.exports = { testLiveTVStreams };
