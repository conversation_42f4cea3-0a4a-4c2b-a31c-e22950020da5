// File: /home/<USER>/NetStream/scripts/scrapeFrenchAnime.js
const logger = require('../src/utils/logger');
const { scrapeFrenchAnimeDetail, isDuplicateAnime: isDuplicateDetail } = require('../src/scrapers/sites/frenchAnime/detail'); // Remove isDuplicateAnime import
const { scrapeFrenchAnimeVfList } = require('../src/scrapers/sites/frenchAnime/vfList');
const { scrapeFrenchAnimeVostfrList } = require('../src/scrapers/sites/frenchAnime/vostfrList');
const { scrapeFrenchAnimeFilmsList } = require('../src/scrapers/sites/frenchAnime/filmsList');
const { enrichItem, enrichItemWithOptions } = require('../src/enrichment/services/enrichService');
const { FRENCH_ANIME_BASE } = require('../src/config/env');
const Config = require('../src/db/models/Config');
const Anime = require('../src/db/models/Anime'); // Add import for deduplication
const { saveToDB } = require('./saveToDB'); // Import saveToDB function

async function isDuplicateAnime(detailUrlPath) { // Define locally with detailUrlPath
  if (!detailUrlPath) return false;
  const existing = await Anime.findOne({ detailUrlPath });
  return !!existing;
}

async function getTotalPages(baseUrl) {
  const axios = require('axios');
  const cheerio = require('cheerio');
  try {
    const response = await axios.get(`${baseUrl}/page/1/`, { timeout: 10000 });
    const $ = cheerio.load(response.data);
    let lastPageLink = $('.pagination .page-numbers:not(.next):last').text().trim() ||
                       $('.pagination .page-item a:last').attr('href') ||
                       $('.pagi-nav .navigation a:last').attr('href') ||
                       $('.pagination a:last').attr('href');
    if (lastPageLink) {
      const match = lastPageLink.match(/page\/(\d+)/) || lastPageLink.match(/\d+$/);
      const maxPages = match ? parseInt(match[1], 10) : parseInt(lastPageLink, 10);
      if (maxPages && maxPages > 0) {
        logger.info(`Detected max pages for ${baseUrl}: ${maxPages}`);
        return maxPages;
      }
    }
    logger.warn(`No pagination detected for ${baseUrl}, defaulting to 1 page`);
    return 1;
  } catch (err) {
    logger.error(`Error detecting pages for ${baseUrl}: ${err.message}`);
    return 1;
  }
}

async function timeout(ms) {
  return new Promise(resolve => setTimeout(resolve, ms));
}

function getProviderFromUrl(url) {
  if (typeof url !== 'string') return 'unknown';
  const fullUrl = url.startsWith('http') ? url : `https:${url}`;
  try {
    const hostname = new URL(fullUrl).hostname.replace('www.', '');
    const domainParts = hostname.split('.');
    const providerName = domainParts[0].charAt(0).toUpperCase() + domainParts[0].slice(1);
    return ['Vidply', 'Magasavor', 'Myvi'].includes(providerName) ? providerName : providerName;
  } catch (err) {
    logger.warn(`Could not parse provider from URL ${fullUrl}: ${err.message}`);
    return 'unknown';
  }
}

async function scrapePage(page, endpoint, isLatest = false) {
  try {
    // Get total pages first to avoid scraping non-existent pages
    const animes = await endpoint.listFn(page);
    
    // Validate response type
    if (!Array.isArray(animes)) {
      logger.error(`Invalid response from ${endpoint.name} list function - expected array but got ${typeof animes}`);
      return [];
    }

    // Log collection info with safe string conversion
    const itemCount = animes.length;
    logger.info(`Collected ${itemCount} anime items from ${endpoint.name}, page ${page}`);

    const animesToSave = [];
    for (const anime of animes) {
      try {
        const detailUrlPath = new URL(anime.detailUrl).pathname;
        const isDuplicate = await isDuplicateAnime(detailUrlPath);

        if (isDuplicate && !isLatest) {
          logger.info(`Skipping duplicate anime: ${anime.title} (path: ${detailUrlPath})`);
          continue;
        }

        logger.info(`Processing anime: ${anime.title}, URL: ${anime.detailUrl}, Season: ${anime.season}, Language: ${anime.animeLanguage}`);
        let details;
        try {
          details = await scrapeFrenchAnimeDetail(anime.detailUrl, anime.season, anime.animeLanguage);
          await timeout(1200);
        } catch (err) {
          logger.warn(`Falling back for ${anime.title}: ${err.message}`);
          details = { episodes: [], metadata: [], streamingUrls: [] };
        }

        // Fetch existing anime from DB for comparison
        const existingAnime = await Anime.findOne({ detailUrlPath });
        const existingEpisodeCount = existingAnime?.episodes?.length || 0;
        const newEpisodeCount = details.episodes.length;

        // Use enrichItemWithOptions to ensure seasons are fetched
        const enriched = await enrichItemWithOptions({ ...anime, ...details }, 'anime', {
            fetchSeasons: true,
            useAdvanced: process.env.USE_ADVANCED_ENRICHMENT === 'true'
        });
        await timeout(1200);

        const episodes = (details.episodes || []).map(ep => ({
          episodeNumber: ep.episodeNumber,
          season: ep.season,
          language: ep.language,
          streamingUrls: (ep.streamingUrls || []).map(url => ({
            url: url.url,
            provider: getProviderFromUrl(url.url),
            language: url.language,
            lastChecked: new Date(),
            isActive: true,
            sourceStreamUrl: null
          }))
        }));

        const baseTitle = enriched.tmdb?.title || enriched.title;
        const animeData = {
          title: baseTitle  || "Default Title",
          detailUrl: anime.detailUrl || 'unknown',
          detailUrlPath: detailUrlPath,
          cleanedTitle: baseTitle,
          season: anime.season || '1',
          episodes,
          metadata: {
            synopsis: details.metadata?.synopsis || enriched.metadata?.synopsis || '',
            actors: details.metadata?.actors || enriched.metadata?.actors || [],
            year: details.metadata?.year || enriched.metadata?.year || '',
            genre: details.metadata?.genre || enriched.metadata?.genre || '',
            creator: details.metadata?.creator || enriched.metadata?.creator || '',
            duration: details.metadata?.duration || enriched.metadata?.duration || ''
          },
          image: enriched.image,
          animeLanguage: anime.animeLanguage || 'unknown',
          tmdb: enriched.tmdb || {},
          jikan: enriched.jikan || {},
          tmdbSeasons: enriched.tmdbSeasons || null,
          tmdbSeason: enriched.tmdbSeason || null,
          jikanSeasons: enriched.jikanSeasons || null,
          updatedAt: new Date() // Always set updatedAt to current date
        };

        // Check if metadata has changed and needs to be updated
        let metadataChanged = false;

        // Check if Jikan data has changed (different MAL ID or new seasons)
        if (existingAnime && existingAnime.jikan && animeData.jikan) {
          if (existingAnime.jikan.mal_id !== animeData.jikan.mal_id) {
            logger.info(`Jikan MAL ID changed for ${anime.title}: ${existingAnime.jikan.mal_id} -> ${animeData.jikan.mal_id}`);
            metadataChanged = true;
          }
        }

        // Check if TMDB data has changed
        if (existingAnime && existingAnime.tmdb && animeData.tmdb) {
          if (existingAnime.tmdb.id !== animeData.tmdb.id) {
            logger.info(`TMDB ID changed for ${anime.title}: ${existingAnime.tmdb.id} -> ${animeData.tmdb.id}`);
            metadataChanged = true;
          }
        }

        // Check if seasons data has changed
        const existingJikanSeasonsCount = existingAnime?.jikanSeasons?.length || 0;
        const newJikanSeasonsCount = animeData.jikanSeasons?.length || 0;
        const existingTmdbSeasonsCount = existingAnime?.tmdbSeasons?.length || 0;
        const newTmdbSeasonsCount = animeData.tmdbSeasons?.length || 0;

        if (existingJikanSeasonsCount !== newJikanSeasonsCount || existingTmdbSeasonsCount !== newTmdbSeasonsCount) {
          logger.info(`Seasons count changed for ${anime.title}: Jikan (${existingJikanSeasonsCount} -> ${newJikanSeasonsCount}), TMDB (${existingTmdbSeasonsCount} -> ${newTmdbSeasonsCount})`);
          metadataChanged = true;
        }

        // Always update if it's a new anime, if there are new episodes, or if metadata has changed
        if (isDuplicate && newEpisodeCount === existingEpisodeCount && !metadataChanged &&
            existingAnime &&
            ((existingAnime.tmdbSeasons && existingAnime.tmdbSeasons.length > 0) ||
             (existingAnime.jikanSeasons && existingAnime.jikanSeasons.length > 0))) {
          logger.info(`Updating anime timestamp: ${anime.title} (path: ${detailUrlPath}) - no changes detected, updating timestamp only`);
          // Update only the updatedAt field
          try {
            await Anime.updateOne({ detailUrlPath }, { $set: { updatedAt: new Date() } });
          } catch (err) {
            logger.error(`Error updating timestamp for ${anime.title}: ${err.message}`);
          }
        } else {
          if (isDuplicate && newEpisodeCount === existingEpisodeCount && metadataChanged) {
            logger.info(`Updating anime: ${anime.title} (path: ${detailUrlPath}) - metadata changes detected, updating database`);
          } else if (isDuplicate && newEpisodeCount === existingEpisodeCount) {
            logger.info(`Updating anime: ${anime.title} (path: ${detailUrlPath}) - no new episodes but updating metadata/seasons`);
          } else if (!isDuplicate) {
            logger.info(`Adding new anime: ${anime.title} (path: ${detailUrlPath})`);
          } else {
            logger.info(`Updating anime: ${anime.title} (path: ${detailUrlPath}) - episode count changed from ${existingEpisodeCount} to ${newEpisodeCount}`);
          }
          animesToSave.push(animeData);
        }
      } catch (err) {
        logger.error(`Error processing anime ${anime.title}: ${err.message}`);
      }
    }

    if (animesToSave.length > 0) {
      logger.info(`Saving ${animesToSave.length} anime items from ${endpoint.name}, page ${page} to database`);
      await saveToDB(animesToSave, 'anime');
      logger.info(`Successfully saved ${animesToSave.length} anime items from page ${page}`);
      return animesToSave;
    }
    logger.warn(`No anime items to save from ${endpoint.name}, page ${page}`);
    return [];
  } catch (err) {
    logger.error(`Error scraping anime page ${page} for ${endpoint.name}: ${err.message}`);
    // Log additional debug information without stringifying the entire objects array
    if (err.animes) {
      logger.debug(`Debug info - Number of anime items: ${err.animes?.length || 0}`);
    }
    return [];
  }
}

async function scrapeFrenchAnime(pageLimit = 1, isLatest = false) {
  try {
    logger.info(`Starting French Anime scrape with page limit: ${pageLimit}`);

    // Get the latest FRENCH_ANIME_BASE from the database
    const frenchAnimeBase = await Config.getValue('FRENCH_ANIME_BASE', FRENCH_ANIME_BASE);
    logger.info(`Using French Anime base URL: ${frenchAnimeBase}`);

    const endpoints = [
      { name: 'VOSTFR', url: `https://${frenchAnimeBase}/animes-vostfr`, listFn: scrapeFrenchAnimeVostfrList },
      { name: 'VF', url: `https://${frenchAnimeBase}/animes-vf`, listFn: scrapeFrenchAnimeVfList },
      { name: 'Films', url: `https://${frenchAnimeBase}/films-vf-vostfr`, listFn: scrapeFrenchAnimeFilmsList }
    ];
    const allAnimesSaved = [];
    for (const endpoint of endpoints) {
      const maxPages = pageLimit === -1 ? await getTotalPages(endpoint.url) : Math.min(pageLimit, await getTotalPages(endpoint.url));
      const pages = Array.from({ length: maxPages }, (_, i) => i + 1);
      logger.info(`Scraping ${endpoint.name} - ${maxPages} pages`);

      const endpointResults = [];
      for (const page of pages) {
        logger.info(`Processing page ${page} for ${endpoint.name}`);
        const pageResult = await scrapePage(page, endpoint, isLatest); // Pass isLatest
        endpointResults.push(...pageResult);
        await timeout(2000);
      }
      allAnimesSaved.push(...endpointResults);
      logger.info(`Finished scraping ${endpoint.name}, saved ${endpointResults.length} items`);
    }

    logger.info(`Total anime items saved across all endpoints: ${allAnimesSaved.length}`);
    return allAnimesSaved;
  } catch (err) {
    logger.error(`Fatal error in scrapeFrenchAnime: ${err.message}`);
    return [];
  }
}

module.exports = { scrapeFrenchAnime };