// File: /home/<USER>/NetStream/scripts/scrapeWitv.js
const logger = require('../src/utils/logger');
const { scrapeWitvDetail } = require('../src/scrapers/sites/witv/detail');
const { scrapeWitvList } = require('../src/scrapers/sites/witv/list');
const { WITV_BASE } = require('../src/config/env');
const LiveTV = require('../src/db/models/LiveTV'); // Import LiveTV model
const Config = require('../src/db/models/Config');
const { normalizeWitvUrl } = require('../src/utils/urlNormalizer');

// Function to check for duplicates based on detailUrlPath
async function isDuplicateLiveTV(detailUrlPath) {
  if (!detailUrlPath) return false; // Avoid querying with null
  const existing = await LiveTV.findOne({ detailUrlPath });
  return !!existing;
}

async function getTotalPages(baseUrl) {
  const axios = require('axios');
  const cheerio = require('cheerio');
  try {
    // Normalize the URL to use the current WITV_BASE domain
    const normalizedUrl = await normalizeWitvUrl(baseUrl);
    logger.info(`Getting total pages for WiTV using normalized URL: ${normalizedUrl}`);

    const response = await axios.get(normalizedUrl, { timeout: 10000 });
    const $ = cheerio.load(response.data);
    const lastPageLink = $('.navigation a:last-child').attr('href');
    if (lastPageLink) {
      const match = lastPageLink.match(/page\/(\d+)/);
      if (match) return parseInt(match[1], 10);
    }
    return 1;
  } catch (err) {
    logger.error(`Error detecting pages for ${baseUrl}: ${err.message}`);
    return 1;
  }
}

async function timeout(ms) {
  return new Promise(resolve => setTimeout(resolve, ms));
}

function getProviderFromUrl(url) {
  if (typeof url !== 'string') return 'unknown';
  const fullUrl = url.startsWith('http') ? url : `https:${url}`;
  try {
    const hostname = new URL(fullUrl).hostname.replace('www.', '');
    const domainParts = hostname.split('.');
    const providerName = domainParts[0].charAt(0).toUpperCase() + domainParts[0].slice(1);
    return ['Vidply', 'Magasavor', 'Myvi'].includes(providerName) ? providerName : providerName;
  } catch (err) {
    logger.warn(`Could not parse provider from URL ${fullUrl}: ${err.message}`);
    return 'unknown';
  }
}

async function scrapeWitv(pageLimit = 0, saveToDB) {
    try {
        // Get the latest WITV_BASE from the database
        const witvBase = await Config.getValue('WITV_BASE', WITV_BASE);
        logger.info(`Using WiTV base URL: ${witvBase}`);

        const endpoint = 'chaines-live';
        const baseUrl = `https://${witvBase}/${endpoint}/`;
        const totalPages = await getTotalPages(baseUrl);
        const maxPages = pageLimit === -1 ? totalPages : Math.min(pageLimit, totalPages);
        logger.info(`Scraping WiTV endpoint ${endpoint} with ${maxPages} pages`);

        const channels = await scrapeWitvList(maxPages);
        logger.info(`Collected ${channels.length} channels from ${endpoint} before deduplication`);

        const channelsToSave = [];
        for (const channel of channels) {
            const detailUrlPath = new URL(channel.detailUrl).pathname;
            const isDuplicate = await isDuplicateLiveTV(detailUrlPath);
            if (isDuplicate) {
                logger.info(`Skipping duplicate channel: ${channel.title} (path: ${detailUrlPath})`);
                continue;
            }

            try {
                logger.info(`Processing channel: ${channel.title} (${channel.detailUrl})`);
                const details = await scrapeWitvDetail(channel.detailUrl);

                const channelData = {
                    title: channel.title || "Default title",
                    detailUrl: channel.detailUrl || "default-url",
                    detailUrlPath: detailUrlPath,
                    cleanedTitle: channel.title,
                    image: details.image,
                    streamingUrls: details.streamingUrls.map(stream => ({
                        url: stream.url,
                        provider: getProviderFromUrl(stream.url),
                        language: stream.language || 'unknown',
                        lastChecked: new Date(),
                        isActive: true,
                        sourceStreamUrl: null
                    })),
                    createdAt: new Date(),
                    updatedAt: new Date()
                };
                channelsToSave.push(channelData);
                await timeout(1000); // Delay per channel detail fetch
            } catch (err) {
                logger.error(`Error processing channel ${channel.title}: ${err.message}`);
            }
        }

        if (channelsToSave.length > 0) {
            logger.info(`Saving ${channelsToSave.length} channels to database`);
            await saveToDB(channelsToSave, 'livetv');
            logger.info(`Successfully saved ${channelsToSave.length} channels`);
            return channelsToSave;
        }

        logger.warn(`No new channels to save for ${endpoint}`);
        return [];
    } catch (err) {
        logger.error(`Fatal error in scrapeWitv: ${err.message}`);
        return [];
    }
}

module.exports = { scrapeWitv };