const mongoose = require('mongoose');
const logger = require('../src/utils/logger');
const Movie = require('../src/db/models/Movie');
const Series = require('../src/db/models/Series');
const Anime = require('../src/db/models/Anime');
const LiveTV = require('../src/db/models/LiveTV');

async function saveToDB(items, type) {
    // Handle parameter order issues - if type is an array and items is a string, swap them
    if (Array.isArray(type) && typeof items === 'string') {
        logger.warn(`Parameter order issue detected - swapping parameters`);
        [items, type] = [type, items];
    }

    // Ensure items is an array
    if (!Array.isArray(items)) {
        throw new Error(`Items parameter must be an array, got: ${typeof items}`);
    }

    // Ensure type is a string
    if (typeof type !== 'string') {
        throw new Error(`Type parameter must be a string, got: ${typeof type} with value: ${type}`);
    }

    const Model = type === 'movie' ? Movie :
        type === 'series' ? Series :
            type === 'anime' ? Anime :
                type === 'livetv' ? LiveTV : null;
    if (!Model) throw new Error(`Unsupported type: ${type}`);

    if (mongoose.connection.readyState !== 1) {
        logger.error('MongoDB connection lost before saving');
        throw new Error('Database connection not active');
    }

    logger.info(`Attempting to save ${items.length} ${type} items to DB`);

    // Track successful operations
    let createdCount = 0;
    let updatedCount = 0;
    let errorCount = 0;

    for (const item of items) {
        try {
            let update = {};
            item.title = item.title || "Default Title";
            item.detailUrl = item.detailUrl || "default-detail-url";

            // Ensure detailUrlPath is properly set
            try {
                item.detailUrlPath = (new URL(item.detailUrl)).pathname;
            } catch (urlError) {
                logger.warn(`Invalid URL format for ${item.title}: ${item.detailUrl}. Using raw value as path.`);
                item.detailUrlPath = item.detailUrl;
            }

            // Ensure required fields are present, even if empty
            if (type === 'movie' || type === 'livetv') {
                item.streamingUrls = item.streamingUrls || [];
            } else if (type === 'series' || type === 'anime') {
                item.episodes = item.episodes || [];
                item.episodes.forEach(ep => {
                    ep.streamingUrls = ep.streamingUrls || [];
                });
            }

            const query = { detailUrlPath: item.detailUrlPath };
            const existing = await Model.findOne(query);

            if (existing) {
                logger.info(`Updating existing ${type}: ${item.title} (ID: ${existing._id})`);
                // Initialize $set with updatedAt
                update['$set'] = { updatedAt: new Date() };

                // Handle episodes or streaming URLs
                if (item.episodes && item.episodes.length > 0) {
                    update['$set'].episodes = item.episodes;
                    logger.info(`Updating ${item.episodes.length} episodes for ${item.title}`);
                } else if (item.streamingUrls && item.streamingUrls.length > 0) {
                    const existingUrls = existing.streamingUrls.map(s => s.url);
                    const newUrls = item.streamingUrls.filter(s => !existingUrls.includes(s.url));
                    if (newUrls.length > 0) {
                        update['$addToSet'] = { streamingUrls: { $each: newUrls } }; // Only add *new* URLs
                        logger.info(`Adding ${newUrls.length} new streaming URLs for ${item.title}`);
                    }
                }

                // Update metadata fields
                if (item.metadata) update['$set'].metadata = { ...existing.metadata, ...item.metadata };

                // Always update the updatedAt field when TMDB data is updated
                if (item.tmdb) {
                    update['$set'].tmdb = item.tmdb;
                    update['$set'].updatedAt = new Date(); // Ensure updatedAt is updated when TMDB data is updated
                    logger.info(`Updating TMDB data for ${item.title}`);
                }

                if (item.jikan) update['$set'].jikan = item.jikan;
                if (item.image) update['$set'].image = item.image;
                if (item.season) update['$set'].season = item.season;
                if (item.thumbnail) update['$set'].thumbnail = item.thumbnail;
                if (item.cleanedTitle) update['$set'].cleanedTitle = item.cleanedTitle;

                // Update seasons data
                if (item.tmdbSeasons && item.tmdbSeasons.length > 0) {
                    update['$set'].tmdbSeasons = item.tmdbSeasons;
                    logger.info(`Updating ${item.tmdbSeasons.length} TMDB seasons for ${item.title}`);
                }

                if (item.tmdbSeason) {
                    update['$set'].tmdbSeason = item.tmdbSeason;
                    logger.info(`Updating TMDB season data for ${item.title} season ${item.tmdbSeason.season_number}`);
                }

                if (item.jikanSeasons && item.jikanSeasons.length > 0) {
                    update['$set'].jikanSeasons = item.jikanSeasons;
                    logger.info(`Updating ${item.jikanSeasons.length} Jikan seasons for ${item.title}`);
                }

                // Always update the updatedAt field, even if there are no other changes
                // Initialize $set if it doesn't exist
                if (!update['$set']) {
                    update['$set'] = {};
                }

                // Always set updatedAt to the current date
                update['$set'].updatedAt = new Date();

                // Log the update operation
                logger.info(`Updating ${type}: ${item.title} with ${Object.keys(update['$set']).length} fields`);

                // Execute the update and get the result
                const updateResult = await Model.updateOne(query, update);

                if (updateResult.modifiedCount > 0) {
                    logger.info(`Successfully updated ${type}: ${item.title} with ${Object.keys(update['$set']).length} update operations`);
                    updatedCount++;
                } else {
                    // If no documents were modified, try a direct updatedAt update
                    logger.warn(`Update operation for ${type}: ${item.title} did not modify any documents. Trying direct updatedAt update.`);

                    // Force update of updatedAt field
                    const timestampResult = await Model.updateOne(query, { $set: { updatedAt: new Date() } });

                    if (timestampResult.modifiedCount > 0) {
                        logger.info(`Updated ${type}: ${item.title} with only updatedAt field`);
                        updatedCount++;
                    } else {
                        logger.warn(`Timestamp update for ${type}: ${item.title} did not modify any documents. Result: ${JSON.stringify(timestampResult)}`);
                    }
                }

            } else {
                logger.info(`Creating new ${type}: ${item.title}`);
                // Explicitly set createdAt and updatedAt for new items
                item.createdAt = new Date();
                item.updatedAt = new Date();

                // Create the new document and get the result
                const newDoc = await Model.create(item);

                if (newDoc && newDoc._id) {
                    logger.info(`Successfully created new ${type}: ${item.title} with ID: ${newDoc._id}`);
                    createdCount++;
                } else {
                    logger.warn(`Creation operation for ${type}: ${item.title} did not return expected result`);
                }
            }
        } catch (err) {
            logger.error(`Error saving ${type} ${item.title}: ${err.message}`, { stack: err.stack });
            errorCount++;
        }
    }

    logger.info(`${items.length} ${type} items processed. Created: ${createdCount}, Updated: ${updatedCount}, Errors: ${errorCount}`);

    // Return statistics for potential further processing
    return {
        total: items.length,
        created: createdCount,
        updated: updatedCount,
        errors: errorCount
    };
}

module.exports = { saveToDB };