#!/usr/bin/env node

/**
 * <PERSON>ript to clean LiveTV data from the database
 *
 * Run with: node scripts/cleanLiveTV.js [options]
 *
 * Options:
 *   --dry-run         Show what would be deleted without actually deleting
 *   --count           Only show count of documents
 *   --backup          Create a JSON backup before deleting
 *   --confirm         Required for actual deletion (safety measure)
 *   --inactive        Only delete inactive channels (isActive: false)
 *   --broken          Only delete channels with broken/empty streaming URLs
 *   --duplicates      Only remove duplicate channels (same title)
 *   --old             Only delete channels older than X days (use with --days)
 *   --days=N          Number of days for --old option (default: 30)
 *   --provider=NAME   Only delete channels from specific provider
 *   --stats           Show detailed statistics about Live TV data
 */

const mongoose = require('mongoose');
const fs = require('fs');
const path = require('path');
const dotenv = require('dotenv');

// Load environment variables from .env file
const result = dotenv.config();
if (result.error) {
  console.error('Error loading .env file:', result.error);
  // Continue anyway, as we might have environment variables set another way
}

// Import the LiveTV model
const LiveTV = require('../src/db/models/LiveTV');
const logger = require('../src/utils/logger');

// Process command line arguments
const argv = process.argv.slice(2);
const options = {
  dryRun: argv.includes('--dry-run'),
  countOnly: argv.includes('--count'),
  backup: argv.includes('--backup'),
  confirm: argv.includes('--confirm'),
  inactive: argv.includes('--inactive'),
  broken: argv.includes('--broken'),
  duplicates: argv.includes('--duplicates'),
  old: argv.includes('--old'),
  stats: argv.includes('--stats'),
  days: parseInt(argv.find(arg => arg.startsWith('--days='))?.split('=')[1]) || 30,
  provider: argv.find(arg => arg.startsWith('--provider='))?.split('=')[1]
};

// Get MongoDB connection URI from environment variables
// We try multiple possible environment variable names
const MONGODB_URI = process.env.MONGODB_URI || 
                   process.env.MONGO_URI || 
                   process.env.DB_URI || 
                   process.env.DATABASE_URI || 
                   'mongodb://localhost:27017/netstream';

logger.info(`Using MongoDB connection string: ${MONGODB_URI.replace(/\/\/([^:]+):([^@]+)@/, '//***:***@')}`);

// Show help if requested
if (argv.includes('--help') || argv.includes('-h')) {
  console.log(`
🧹 LiveTV Database Cleaner

Usage: node scripts/cleanLiveTV.js [options]

Options:
  --help, -h        Show this help message
  --dry-run         Show what would be deleted without actually deleting
  --count           Only show count of documents
  --backup          Create a JSON backup before deleting
  --confirm         Required for actual deletion (safety measure)
  --stats           Show detailed statistics about Live TV data

Selective Cleaning Options:
  --inactive        Only delete inactive channels (isActive: false)
  --broken          Only delete channels with broken/empty streaming URLs
  --duplicates      Only remove duplicate channels (same title)
  --old             Only delete channels older than X days (use with --days)
  --days=N          Number of days for --old option (default: 30)
  --provider=NAME   Only delete channels from specific provider

Examples:
  # Show statistics
  node scripts/cleanLiveTV.js --stats

  # Dry run to see what would be deleted
  node scripts/cleanLiveTV.js --dry-run

  # Delete only inactive channels with backup
  node scripts/cleanLiveTV.js --inactive --backup --confirm

  # Remove duplicates
  node scripts/cleanLiveTV.js --duplicates --confirm

  # Delete old channels (older than 7 days)
  node scripts/cleanLiveTV.js --old --days=7 --confirm

  # Delete all channels from specific provider
  node scripts/cleanLiveTV.js --provider=WiTV --confirm

  # Delete all channels (DANGEROUS!)
  node scripts/cleanLiveTV.js --backup --confirm
`);
  process.exit(0);
}

// Helper functions
async function getStatistics() {
  const totalCount = await LiveTV.countDocuments();
  const activeCount = await LiveTV.countDocuments({ isActive: true });
  const inactiveCount = await LiveTV.countDocuments({ isActive: false });

  // Count channels with broken URLs
  const brokenCount = await LiveTV.countDocuments({
    $or: [
      { streamingUrls: { $exists: false } },
      { streamingUrls: { $size: 0 } },
      { 'streamingUrls.url': { $exists: false } },
      { 'streamingUrls.url': '' }
    ]
  });

  // Count by provider
  const providerStats = await LiveTV.aggregate([
    { $unwind: '$streamingUrls' },
    { $group: { _id: '$streamingUrls.provider', count: { $sum: 1 } } },
    { $sort: { count: -1 } }
  ]);

  // Count duplicates (same title)
  const duplicateStats = await LiveTV.aggregate([
    { $group: { _id: '$title', count: { $sum: 1 } } },
    { $match: { count: { $gt: 1 } } },
    { $group: { _id: null, totalDuplicates: { $sum: '$count' }, uniqueTitles: { $sum: 1 } } }
  ]);

  // Count old channels
  const cutoffDate = new Date();
  cutoffDate.setDate(cutoffDate.getDate() - options.days);
  const oldCount = await LiveTV.countDocuments({
    $or: [
      { createdAt: { $lt: cutoffDate } },
      { lastUpdated: { $lt: cutoffDate } }
    ]
  });

  return {
    total: totalCount,
    active: activeCount,
    inactive: inactiveCount,
    broken: brokenCount,
    old: oldCount,
    providers: providerStats,
    duplicates: duplicateStats[0] || { totalDuplicates: 0, uniqueTitles: 0 }
  };
}

function buildDeleteQuery() {
  const query = {};
  const conditions = [];

  if (options.inactive) {
    conditions.push({ isActive: false });
  }

  if (options.broken) {
    conditions.push({
      $or: [
        { streamingUrls: { $exists: false } },
        { streamingUrls: { $size: 0 } },
        { 'streamingUrls.url': { $exists: false } },
        { 'streamingUrls.url': '' }
      ]
    });
  }

  if (options.old) {
    const cutoffDate = new Date();
    cutoffDate.setDate(cutoffDate.getDate() - options.days);
    conditions.push({
      $or: [
        { createdAt: { $lt: cutoffDate } },
        { lastUpdated: { $lt: cutoffDate } }
      ]
    });
  }

  if (options.provider) {
    conditions.push({ 'streamingUrls.provider': options.provider });
  }

  if (conditions.length > 0) {
    query.$and = conditions;
  }

  return query;
}

async function main() {
  try {
    logger.info('Connecting to MongoDB...');
    await mongoose.connect(MONGODB_URI, {
      useNewUrlParser: true,
      useUnifiedTopology: true
    });
    logger.info('Connected to MongoDB');

    // Show statistics if requested
    if (options.stats) {
      logger.info('📊 Live TV Database Statistics:');
      const stats = await getStatistics();

      logger.info(`📺 Total channels: ${stats.total}`);
      logger.info(`✅ Active channels: ${stats.active}`);
      logger.info(`❌ Inactive channels: ${stats.inactive}`);
      logger.info(`🔗 Broken channels (no/empty URLs): ${stats.broken}`);
      logger.info(`📅 Old channels (>${options.days} days): ${stats.old}`);
      logger.info(`🔄 Duplicate titles: ${stats.duplicates.uniqueTitles} titles with ${stats.duplicates.totalDuplicates} total channels`);

      if (stats.providers.length > 0) {
        logger.info('📡 Channels by provider:');
        stats.providers.forEach(provider => {
          logger.info(`   ${provider._id}: ${provider.count} channels`);
        });
      }

      if (options.countOnly) {
        process.exit(0);
      }
    }

    // Build query based on options
    const deleteQuery = buildDeleteQuery();
    const isSelectiveDelete = Object.keys(deleteQuery).length > 0;

    // Get count of documents to be deleted
    const count = isSelectiveDelete ?
      await LiveTV.countDocuments(deleteQuery) :
      await LiveTV.countDocuments();

    logger.info(`Found ${count} LiveTV documents matching criteria`);

    if (count === 0) {
      logger.info('No LiveTV documents to delete. Exiting.');
      process.exit(0);
    }

    if (options.countOnly) {
      process.exit(0);
    }

    // Handle duplicates removal
    if (options.duplicates) {
      logger.info('🔄 Finding and removing duplicate channels...');

      const duplicates = await LiveTV.aggregate([
        { $group: {
          _id: '$title',
          docs: { $push: '$_id' },
          count: { $sum: 1 }
        }},
        { $match: { count: { $gt: 1 } } }
      ]);

      let duplicatesRemoved = 0;
      for (const duplicate of duplicates) {
        // Keep the first document, remove the rest
        const docsToRemove = duplicate.docs.slice(1);
        if (!options.dryRun && options.confirm) {
          const result = await LiveTV.deleteMany({ _id: { $in: docsToRemove } });
          duplicatesRemoved += result.deletedCount;
        } else {
          duplicatesRemoved += docsToRemove.length;
        }
        logger.info(`${options.dryRun ? 'Would remove' : 'Removed'} ${docsToRemove.length} duplicates of "${duplicate._id}"`);
      }

      logger.info(`${options.dryRun ? 'Would remove' : 'Removed'} ${duplicatesRemoved} duplicate channels`);

      if (!isSelectiveDelete || options.dryRun) {
        process.exit(0);
      }
    }

    // Create backup if requested
    if (options.backup) {
      const backupDir = path.join(__dirname, '../backups');

      // Create backups directory if it doesn't exist
      if (!fs.existsSync(backupDir)) {
        fs.mkdirSync(backupDir, { recursive: true });
      }

      const timestamp = new Date().toISOString().replace(/:/g, '-');
      const backupFile = path.join(backupDir, `livetv_backup_${timestamp}.json`);

      logger.info('💾 Creating backup of LiveTV data...');

      // Fetch documents to be deleted with lean() for better performance
      const documentsToBackup = isSelectiveDelete ?
        await LiveTV.find(deleteQuery).lean() :
        await LiveTV.find().lean();

      // Write to file
      fs.writeFileSync(
        backupFile,
        JSON.stringify(documentsToBackup, null, 2)
      );

      logger.info(`💾 Backup created at: ${backupFile} (${documentsToBackup.length} documents)`);
    }

    if (options.dryRun) {
      if (isSelectiveDelete) {
        logger.info(`🔍 DRY RUN: Would delete ${count} LiveTV documents matching criteria:`);
        if (options.inactive) logger.info('   - Inactive channels');
        if (options.broken) logger.info('   - Channels with broken/empty URLs');
        if (options.old) logger.info(`   - Channels older than ${options.days} days`);
        if (options.provider) logger.info(`   - Channels from provider: ${options.provider}`);
      } else {
        logger.info(`🔍 DRY RUN: Would delete ALL ${count} LiveTV documents`);
      }
      logger.info('To actually delete, run without --dry-run and with --confirm');
      process.exit(0);
    }

    // Require explicit confirmation for deletion
    if (!options.confirm) {
      if (isSelectiveDelete) {
        logger.warn(`⚠️  SAFETY CHECK: This will delete ${count} LiveTV documents matching your criteria!`);
      } else {
        logger.warn(`⚠️  SAFETY CHECK: This will delete ALL ${count} LiveTV documents!`);
      }
      logger.warn('To confirm deletion, run again with --confirm flag');
      process.exit(1);
    }

    // Delete documents
    if (isSelectiveDelete) {
      logger.info(`🗑️  Deleting ${count} LiveTV documents matching criteria...`);
      if (options.inactive) logger.info('   - Deleting inactive channels');
      if (options.broken) logger.info('   - Deleting channels with broken/empty URLs');
      if (options.old) logger.info(`   - Deleting channels older than ${options.days} days`);
      if (options.provider) logger.info(`   - Deleting channels from provider: ${options.provider}`);
    } else {
      logger.info(`🗑️  Deleting ALL ${count} LiveTV documents...`);
    }

    const result = await LiveTV.deleteMany(deleteQuery);

    logger.info(`✅ Successfully deleted ${result.deletedCount} LiveTV documents`);

    // Show remaining count
    const remainingCount = await LiveTV.countDocuments();
    logger.info(`📊 Remaining LiveTV documents in database: ${remainingCount}`);
    
  } catch (error) {
    logger.error(`Error: ${error.message}`, { stack: error.stack });
    process.exit(1);
  } finally {
    if (mongoose.connection.readyState !== 0) {
      await mongoose.disconnect();
      logger.info('Disconnected from MongoDB');
    }
  }
}

main(); 