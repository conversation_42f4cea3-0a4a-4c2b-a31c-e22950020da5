// File: scripts/server_startup_enrichment.js
require('dotenv').config();
const mongoose = require('mongoose');
const logger = require('../src/utils/logger');
const Series = require('../src/db/models/Series');
const Anime = require('../src/db/models/Anime');
const { enrichItemWithOptions } = require('../src/enrichment/services/enrichService');
const { saveToDB } = require('./saveToDB');
const pLimit = require('p-limit');

// Limit concurrent operations to avoid overwhelming the API
const limit = pLimit(5);

/**
 * Updates all series and anime in the database with seasons data
 * This script is meant to be run after the server starts
 */
async function updateAllSeasonsOnStartup() {
  try {
    // Connect to MongoDB
    const mongoUri = process.env.MONGO_URI;
    logger.info(`[Startup Enrichment] Connecting to MongoDB: ${mongoUri}`);
    await mongoose.connect(mongoUri);
    logger.info('[Startup Enrichment] Connected to MongoDB');

    // Get command line arguments
    const useAdvanced = process.env.USE_ADVANCED_ENRICHMENT === 'true';
    const limitCount = parseInt(process.env.STARTUP_ENRICHMENT_LIMIT || '10', 10);
    
    logger.info(`[Startup Enrichment] Options: Advanced=${useAdvanced}, Limit=${limitCount}`);

    // Update series
    await updateSeriesSeasons(limitCount, useAdvanced);

    // Update anime
    await updateAnimeSeasons(limitCount, useAdvanced);

    // Disconnect from MongoDB
    await mongoose.disconnect();
    logger.info('[Startup Enrichment] Disconnected from MongoDB');
  } catch (error) {
    logger.error(`[Startup Enrichment] Error updating seasons: ${error.message}`);
    if (mongoose.connection.readyState === 1) {
      await mongoose.disconnect();
    }
  }
}

async function updateSeriesSeasons(limitCount, useAdvanced) {
  // Find series with TMDB ID but no seasons
  const query = {
    'tmdb.id': { $exists: true, $ne: null },
    $or: [
      { tmdbSeasons: { $exists: false } },
      { tmdbSeasons: { $size: 0 } }
    ]
  };
  
  const series = await Series.find(query).limit(limitCount);
  logger.info(`[Startup Enrichment] Found ${series.length} series to update with seasons data`);

  let successCount = 0;
  let errorCount = 0;

  // Process each series
  await Promise.all(series.map(item => limit(async () => {
    try {
      logger.info(`[Startup Enrichment] Processing series: ${item.title} (TMDB ID: ${item.tmdb?.id})`);
      
      // Enrich the series
      const enriched = await enrichItemWithOptions(item.toObject(), 'series', {
        useAdvanced,
        fetchSeasons: true
      });
      
      if (enriched.tmdbSeasons && enriched.tmdbSeasons.length > 0) {
        logger.info(`[Startup Enrichment] Enriched ${item.title} with ${enriched.tmdbSeasons.length} seasons`);
        
        // Save to database
        await saveToDB([enriched], 'series');
        logger.info(`[Startup Enrichment] Updated series: ${item.title}`);
        successCount++;
      } else {
        logger.warn(`[Startup Enrichment] No seasons found for ${item.title}`);
        errorCount++;
      }
    } catch (error) {
      logger.error(`[Startup Enrichment] Error updating series ${item.title}: ${error.message}`);
      errorCount++;
    }
  })));

  logger.info(`[Startup Enrichment] Series update complete: ${successCount} successful, ${errorCount} failed`);
}

async function updateAnimeSeasons(limitCount, useAdvanced) {
  // Find anime with Jikan ID but no seasons
  const query = {
    'jikan.mal_id': { $exists: true, $ne: null },
    $or: [
      { jikanSeasons: { $exists: false } },
      { jikanSeasons: { $size: 0 } }
    ]
  };
  
  const animes = await Anime.find(query).limit(limitCount);
  logger.info(`[Startup Enrichment] Found ${animes.length} anime to update with seasons data`);

  let successCount = 0;
  let errorCount = 0;

  // Process each anime
  await Promise.all(animes.map(item => limit(async () => {
    try {
      logger.info(`[Startup Enrichment] Processing anime: ${item.title} (MAL ID: ${item.jikan?.mal_id})`);
      
      // Enrich the anime
      const enriched = await enrichItemWithOptions(item.toObject(), 'anime', {
        useAdvanced,
        fetchSeasons: true
      });
      
      if ((enriched.tmdbSeasons && enriched.tmdbSeasons.length > 0) || 
          (enriched.jikanSeasons && enriched.jikanSeasons.length > 0)) {
        logger.info(`[Startup Enrichment] Enriched ${item.title} with ${enriched.tmdbSeasons?.length || 0} TMDB seasons and ${enriched.jikanSeasons?.length || 0} Jikan seasons`);
        
        // Save to database
        await saveToDB([enriched], 'anime');
        logger.info(`[Startup Enrichment] Updated anime: ${item.title}`);
        successCount++;
      } else {
        logger.warn(`[Startup Enrichment] No seasons found for ${item.title}`);
        errorCount++;
      }
    } catch (error) {
      logger.error(`[Startup Enrichment] Error updating anime ${item.title}: ${error.message}`);
      errorCount++;
    }
  })));

  logger.info(`[Startup Enrichment] Anime update complete: ${successCount} successful, ${errorCount} failed`);
}

// If this script is run directly
if (require.main === module) {
  updateAllSeasonsOnStartup();
}

module.exports = { updateAllSeasonsOnStartup };
