#!/usr/bin/env node

/**
 * <PERSON>ript to migrate Live TV data from 'livetvs' collection to 'livetv' collection
 * This fixes the Mongoose pluralization issue where new data was saved to 'livetvs'
 * but the GraphQL resolvers expect data in 'livetv'
 */

const mongoose = require('mongoose');
const dotenv = require('dotenv');

// Load environment variables
dotenv.config();

const MONGODB_URI = process.env.MONGODB_URI || 
                   process.env.MONGO_URI || 
                   'mongodb://localhost:27017/netstream';

async function migrateLiveTVCollections() {
  try {
    console.log('🔄 Starting Live TV collection migration...');
    
    // Connect to MongoDB
    await mongoose.connect(MONGODB_URI, {
      useNewUrlParser: true,
      useUnifiedTopology: true
    });
    console.log('✅ Connected to MongoDB');

    const db = mongoose.connection.db;
    
    // Check if collections exist
    const collections = await db.listCollections().toArray();
    const collectionNames = collections.map(c => c.name);
    
    const hasLivetvs = collectionNames.includes('livetvs');
    const hasLivetv = collectionNames.includes('livetv');
    
    console.log(`📊 Collection status:`);
    console.log(`   - 'livetvs' exists: ${hasLivetvs}`);
    console.log(`   - 'livetv' exists: ${hasLivetv}`);
    
    if (!hasLivetvs) {
      console.log('ℹ️  No migration needed - livetvs collection does not exist');
      return;
    }
    
    // Get counts
    const livetvs = db.collection('livetvs');
    const livetv = db.collection('livetv');
    
    const livetvs_count = await livetvs.countDocuments();
    const livetv_count = hasLivetv ? await livetv.countDocuments() : 0;
    
    console.log(`📈 Document counts:`);
    console.log(`   - 'livetvs': ${livetvs_count} documents`);
    console.log(`   - 'livetv': ${livetv_count} documents`);
    
    if (livetvs_count === 0) {
      console.log('ℹ️  No documents to migrate from livetvs collection');
      return;
    }
    
    // Get sample documents to compare
    const sampleLivetvs = await livetvs.findOne();
    const sampleLivetv = hasLivetv ? await livetv.findOne() : null;
    
    console.log('\n🔍 Sample document comparison:');
    console.log('livetvs sample:', JSON.stringify({
      title: sampleLivetvs?.title,
      hasImage: !!sampleLivetvs?.image,
      hasTmdb: !!sampleLivetvs?.tmdb,
      streamingUrlsCount: sampleLivetvs?.streamingUrls?.length || 0,
      createdAt: sampleLivetvs?.createdAt
    }, null, 2));
    
    if (sampleLivetv) {
      console.log('livetv sample:', JSON.stringify({
        title: sampleLivetv?.title,
        hasImage: !!sampleLivetv?.image,
        hasTmdb: !!sampleLivetv?.tmdb,
        streamingUrlsCount: sampleLivetv?.streamingUrls?.length || 0,
        createdAt: sampleLivetv?.createdAt
      }, null, 2));
    }
    
    // Ask for confirmation
    console.log('\n⚠️  Migration Strategy:');
    if (livetv_count > 0) {
      console.log('   1. Backup existing livetv collection');
      console.log('   2. Merge new livetvs data with existing livetv data');
      console.log('   3. Prefer newer data when conflicts exist');
    } else {
      console.log('   1. Copy all data from livetvs to livetv');
    }
    console.log('   4. Rename livetvs to livetvs_backup');
    
    // Perform migration
    console.log('\n🚀 Starting migration...');
    
    // Step 1: Backup existing livetv if it exists
    if (livetv_count > 0) {
      const backupName = `livetv_backup_${Date.now()}`;
      console.log(`📦 Creating backup: ${backupName}`);
      await db.collection('livetv').aggregate([
        { $out: backupName }
      ]).toArray();
      console.log(`✅ Backup created: ${backupName}`);
    }
    
    // Step 2: Get all documents from livetvs
    console.log('📥 Fetching documents from livetvs...');
    const livetvsDocs = await livetvs.find({}).toArray();
    console.log(`✅ Fetched ${livetvsDocs.length} documents from livetvs`);
    
    // Step 3: Upsert documents to livetv collection
    console.log('💾 Upserting documents to livetv collection...');
    let upsertedCount = 0;
    let updatedCount = 0;
    
    for (const doc of livetvsDocs) {
      try {
        const result = await livetv.updateOne(
          { detailUrlPath: doc.detailUrlPath },
          { 
            $set: { 
              ...doc,
              updatedAt: new Date()
            }
          },
          { upsert: true }
        );
        
        if (result.upsertedCount > 0) {
          upsertedCount++;
        } else if (result.modifiedCount > 0) {
          updatedCount++;
        }
      } catch (error) {
        console.error(`❌ Error upserting document ${doc.title}:`, error.message);
      }
    }
    
    console.log(`✅ Migration completed:`);
    console.log(`   - ${upsertedCount} new documents inserted`);
    console.log(`   - ${updatedCount} existing documents updated`);
    
    // Step 4: Rename livetvs collection to backup
    console.log('🗂️  Renaming livetvs collection to backup...');
    const backupCollectionName = `livetvs_backup_${Date.now()}`;
    await db.collection('livetvs').rename(backupCollectionName);
    console.log(`✅ Renamed livetvs to ${backupCollectionName}`);
    
    // Step 5: Verify final state
    const finalCount = await livetv.countDocuments();
    console.log(`\n📊 Final verification:`);
    console.log(`   - livetv collection now has ${finalCount} documents`);
    console.log(`   - livetvs collection has been renamed to ${backupCollectionName}`);
    
    console.log('\n🎉 Migration completed successfully!');
    console.log('   The GraphQL resolvers should now find Live TV data in the correct collection.');
    
  } catch (error) {
    console.error('💥 Migration failed:', error);
    process.exit(1);
  } finally {
    await mongoose.disconnect();
    console.log('🔌 Disconnected from MongoDB');
  }
}

// Run the migration
if (require.main === module) {
  migrateLiveTVCollections().catch(error => {
    console.error('💥 Unexpected error:', error);
    process.exit(1);
  });
}

module.exports = { migrateLiveTVCollections };
