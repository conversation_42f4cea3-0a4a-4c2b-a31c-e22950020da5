# Dockerfile.free - Optimized for Render.com Free Tier
# Multi-stage build to minimize final image size

# Stage 1: Backend Dependencies
FROM node:18-alpine AS backend-deps

WORKDIR /app

# Copy backend package files
COPY package.json package-lock.json ./

# Install only essential production dependencies
RUN npm ci --only=production --no-audit --no-fund && \
    npm cache clean --force

# Stage 2: Frontend Build
FROM node:18-alpine AS frontend-build

WORKDIR /app

# Copy frontend package files
COPY netstream-nextjs/package.json netstream-nextjs/package-lock.json ./netstream-nextjs/

# Install frontend dependencies
WORKDIR /app/netstream-nextjs
RUN npm ci --only=production --no-audit --no-fund

# Copy frontend source
COPY netstream-nextjs/ ./

# Build frontend for static export
RUN npm run build:free

# Stage 3: Production Runtime
FROM node:18-alpine AS runtime

# Install curl for health checks
RUN apk add --no-cache curl

WORKDIR /app

# Create non-root user for security
RUN addgroup -g 1001 -S nodejs && \
    adduser -S netstream -u 1001

# Copy backend dependencies
COPY --from=backend-deps /app/node_modules ./node_modules

# Copy backend source (minimal files only)
COPY src/ ./src/
COPY server-free.js ./
COPY package.json ./
COPY schema-free.graphql ./

# Copy frontend static build
COPY --from=frontend-build /app/netstream-nextjs/out ./public

# Set ownership
RUN chown -R netstream:nodejs /app

# Switch to non-root user
USER netstream

# Expose port
EXPOSE 3000

# Health check
HEALTHCHECK --interval=30s --timeout=10s --start-period=60s --retries=3 \
  CMD curl -f http://localhost:3000/health || exit 1

# Start the application
CMD ["node", "server-free.js"]
